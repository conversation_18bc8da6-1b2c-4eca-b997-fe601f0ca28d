@page "/profile"
@using LegendXManager.Shared.Components.Auth
@using LegendXManager.Shared.Services.Auth
@using Microsoft.AspNetCore.Authorization
@attribute [Authorize]
@inject IAuthService AuthService
@inject NavigationManager Navigation

<PageTitle>Profile - LegendX Manager</PageTitle>

@if (isAuthenticated)
{
    <ProfileManagement />
}
else
{
    <div class="auth-required">
        <div class="auth-required-content">
            <div class="auth-required-icon">
                <i class="bi bi-person-lock"></i>
            </div>
            <h2>Authentication Required</h2>
            <p>You need to be logged in to view your profile.</p>
            <button class="btn btn-primary" @onclick="GoToLogin">
                <i class="bi bi-box-arrow-in-right"></i>
                Sign In
            </button>
        </div>
    </div>
}

<style>
    .auth-required {
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 60vh;
        padding: 40px;
    }
    
    .auth-required-content {
        text-align: center;
        max-width: 400px;
    }
    
    .auth-required-icon {
        font-size: 64px;
        color: #667eea;
        margin-bottom: 24px;
    }
    
    .auth-required h2 {
        color: #2d3748;
        margin-bottom: 16px;
    }
    
    .auth-required p {
        color: #718096;
        margin-bottom: 24px;
    }
</style>

@code {
    private bool isAuthenticated = false;

    protected override async Task OnInitializedAsync()
    {
        isAuthenticated = await AuthService.IsAuthenticatedAsync();
        
        if (!isAuthenticated)
        {
            // Optionally redirect to login with return URL
            // Navigation.NavigateTo($"/login?returnUrl={Uri.EscapeDataString("/profile")}");
        }
    }

    private void GoToLogin()
    {
        Navigation.NavigateTo($"/login?returnUrl={Uri.EscapeDataString("/profile")}");
    }
}
