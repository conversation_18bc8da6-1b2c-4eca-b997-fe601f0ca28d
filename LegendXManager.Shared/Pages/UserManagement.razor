@page "/admin/users"
@using LegendXManager.Shared.Components.Admin
@using LegendXManager.Shared.Services.Auth
@using Microsoft.AspNetCore.Authorization
@attribute [Authorize(Roles = "Admin")]
@inject IAuthService AuthService
@inject NavigationManager Navigation

<PageTitle>User Management - LegendX Manager</PageTitle>

@if (hasPermission)
{
    <UserManagement />
}
else
{
    <div class="access-denied">
        <div class="access-denied-content">
            <div class="access-denied-icon">
                <i class="bi bi-shield-exclamation"></i>
            </div>
            <h2>Access Denied</h2>
            <p>You don't have permission to access user management.</p>
            <p>Please contact your administrator if you believe this is an error.</p>
            <button class="btn btn-primary" @onclick="GoBack">
                <i class="bi bi-arrow-left"></i>
                Go Back
            </button>
        </div>
    </div>
}

<style>
    .access-denied {
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 60vh;
        padding: 40px;
    }
    
    .access-denied-content {
        text-align: center;
        max-width: 400px;
    }
    
    .access-denied-icon {
        font-size: 64px;
        color: #e53e3e;
        margin-bottom: 24px;
    }
    
    .access-denied h2 {
        color: #2d3748;
        margin-bottom: 16px;
    }
    
    .access-denied p {
        color: #718096;
        margin-bottom: 16px;
    }
</style>

@code {
    private bool hasPermission = false;

    protected override async Task OnInitializedAsync()
    {
        // Check if user has admin role or user management permission
        hasPermission = await AuthService.HasRoleAsync("Admin") || 
                       await AuthService.HasPermissionAsync("UserManagement");
        
        if (!hasPermission)
        {
            // Log access attempt
            var currentUser = await AuthService.GetCurrentUserAsync();
            Console.WriteLine($"Unauthorized access attempt to user management by user: {currentUser?.Username}");
        }
    }

    private void GoBack()
    {
        Navigation.NavigateTo("/");
    }
}
