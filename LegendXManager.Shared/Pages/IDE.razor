@page "/ide"
@using LegendXManager.Shared.Components.IDE
@inject IJSRuntime JSRuntime

<PageTitle>IDE</PageTitle>

<div class="ide-container">
    <div class="ide-header">
        <div class="ide-toolbar">
            <div class="toolbar-section">
                <button class="btn btn-sm btn-outline-light me-2" @onclick="NewFile">
                    <i class="bi bi-file-plus"></i> New
                </button>
                <button class="btn btn-sm btn-outline-light me-2" @onclick="OpenFile">
                    <i class="bi bi-folder2-open"></i> Open
                </button>
                <button class="btn btn-sm btn-outline-light me-2" @onclick="SaveFile">
                    <i class="bi bi-floppy"></i> Save
                </button>
            </div>
            <div class="toolbar-section">
                <button class="btn btn-sm btn-outline-light me-2" @onclick="RunCode">
                    <i class="bi bi-play-fill"></i> Run
                </button>
                <button class="btn btn-sm btn-outline-light me-2" @onclick="ToggleTerminal">
                    <i class="bi bi-terminal"></i> Terminal
                </button>
            </div>
        </div>
    </div>

    <div class="ide-body">
        <div class="ide-sidebar">
            <FileExplorer @ref="fileExplorer" OnFileSelected="OnFileSelected" />
        </div>

        <div class="ide-main">
            <div class="editor-container">
                <div class="editor-tabs">
                    @if (openTabs.Any())
                    {
                        @foreach (var tab in openTabs)
                        {
                            <div class="editor-tab @(tab.IsActive ? "active" : "")" @onclick="() => SetActiveTab(tab)">
                                <span class="tab-title">@tab.FileName</span>
                                <button class="tab-close" @onclick="() => CloseTab(tab)" @onclick:stopPropagation="true">
                                    <i class="bi bi-x"></i>
                                </button>
                            </div>
                        }
                    }
                    else
                    {
                        <div class="no-tabs">No files open</div>
                    }
                </div>

                <div class="editor-content">
                    @if (activeTab != null)
                    {
                        <CodeEditor @ref="codeEditor" 
                                   Content="@activeTab.Content" 
                                   Language="@activeTab.Language"
                                   OnContentChanged="OnContentChanged" />
                    }
                    else
                    {
                        <div class="welcome-screen">
                            <div class="welcome-content">
                                <h3>Welcome to LegendX IDE</h3>
                                <p>Open a file to start editing, or create a new file.</p>
                                <div class="welcome-actions">
                                    <button class="btn btn-primary me-2" @onclick="NewFile">
                                        <i class="bi bi-file-plus"></i> New File
                                    </button>
                                    <button class="btn btn-outline-primary" @onclick="OpenFile">
                                        <i class="bi bi-folder2-open"></i> Open File
                                    </button>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </div>

            @if (showTerminal)
            {
                <div class="terminal-container">
                    <div class="terminal-header">
                        <span class="terminal-title">Terminal</span>
                        <button class="btn btn-sm btn-outline-light" @onclick="ToggleTerminal">
                            <i class="bi bi-x"></i>
                        </button>
                    </div>
                    <Terminal @ref="terminal" />
                </div>
            }
        </div>
    </div>

    <div class="ide-footer">
        <StatusBar CurrentFile="@(activeTab?.FileName)" 
                   LineNumber="@currentLine" 
                   ColumnNumber="@currentColumn" 
                   Language="@(activeTab?.Language)" />
    </div>
</div>

@code {
    private FileExplorer? fileExplorer;
    private CodeEditor? codeEditor;
    private Terminal? terminal;
    
    private List<EditorTab> openTabs = new();
    private EditorTab? activeTab;
    private bool showTerminal = false;
    private int currentLine = 1;
    private int currentColumn = 1;

    public class EditorTab
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string FileName { get; set; } = "";
        public string Content { get; set; } = "";
        public string Language { get; set; } = "plaintext";
        public bool IsActive { get; set; }
        public bool IsDirty { get; set; }
        public string? FilePath { get; set; }
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            // Initialize IDE
            await JSRuntime.InvokeVoidAsync("initializeIDE");
        }
    }

    private void NewFile()
    {
        var newTab = new EditorTab
        {
            FileName = $"untitled-{openTabs.Count + 1}.txt",
            Content = "",
            Language = "plaintext",
            IsActive = true
        };

        // Deactivate other tabs
        foreach (var tab in openTabs)
            tab.IsActive = false;

        openTabs.Add(newTab);
        activeTab = newTab;
        StateHasChanged();
    }

    private async Task OpenFile()
    {
        // This would typically open a file dialog
        // For now, we'll simulate opening a file
        var newTab = new EditorTab
        {
            FileName = "example.cs",
            Content = @"using System;

namespace Example
{
    public class Program
    {
        public static void Main(string[] args)
        {
            Console.WriteLine(""Hello, World!"");
        }
    }
}",
            Language = "csharp",
            IsActive = true,
            FilePath = "/example.cs"
        };

        // Deactivate other tabs
        foreach (var tab in openTabs)
            tab.IsActive = false;

        openTabs.Add(newTab);
        activeTab = newTab;
        StateHasChanged();
    }

    private async Task SaveFile()
    {
        if (activeTab != null)
        {
            // Implement save logic here
            activeTab.IsDirty = false;
            await JSRuntime.InvokeVoidAsync("showNotification", $"Saved {activeTab.FileName}");
        }
    }

    private async Task RunCode()
    {
        if (activeTab != null)
        {
            showTerminal = true;
            StateHasChanged();
            
            // Simulate running code
            if (terminal != null)
            {
                await terminal.WriteOutput($"Running {activeTab.FileName}...\n");
                await Task.Delay(1000);
                await terminal.WriteOutput("Execution completed.\n");
            }
        }
    }

    private void ToggleTerminal()
    {
        showTerminal = !showTerminal;
        StateHasChanged();
    }

    private void SetActiveTab(EditorTab tab)
    {
        foreach (var t in openTabs)
            t.IsActive = false;
        
        tab.IsActive = true;
        activeTab = tab;
        StateHasChanged();
    }

    private void CloseTab(EditorTab tab)
    {
        openTabs.Remove(tab);
        
        if (tab == activeTab)
        {
            activeTab = openTabs.LastOrDefault();
            if (activeTab != null)
                activeTab.IsActive = true;
        }
        
        StateHasChanged();
    }

    private void OnFileSelected(string filePath, string fileName)
    {
        // Check if file is already open
        var existingTab = openTabs.FirstOrDefault(t => t.FilePath == filePath);
        if (existingTab != null)
        {
            SetActiveTab(existingTab);
            return;
        }

        // Open new tab
        var language = GetLanguageFromFileName(fileName);
        var content = GetFileContent(filePath); // This would load actual file content
        
        var newTab = new EditorTab
        {
            FileName = fileName,
            Content = content,
            Language = language,
            IsActive = true,
            FilePath = filePath
        };

        // Deactivate other tabs
        foreach (var tab in openTabs)
            tab.IsActive = false;

        openTabs.Add(newTab);
        activeTab = newTab;
        StateHasChanged();
    }

    private void OnContentChanged(string content)
    {
        if (activeTab != null)
        {
            activeTab.Content = content;
            activeTab.IsDirty = true;
        }
    }

    private string GetLanguageFromFileName(string fileName)
    {
        var extension = Path.GetExtension(fileName).ToLower();
        return extension switch
        {
            ".cs" => "csharp",
            ".js" => "javascript",
            ".ts" => "typescript",
            ".html" => "html",
            ".css" => "css",
            ".json" => "json",
            ".xml" => "xml",
            ".razor" => "razor",
            ".py" => "python",
            ".java" => "java",
            ".cpp" or ".c" => "cpp",
            _ => "plaintext"
        };
    }

    private string GetFileContent(string filePath)
    {
        // This would load actual file content
        // For demo purposes, return sample content
        return $"// Content of {filePath}\n// This is a sample file\n\nConsole.WriteLine(\"Hello from {Path.GetFileName(filePath)}\");";
    }
}
