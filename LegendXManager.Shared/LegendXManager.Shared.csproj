<Project Sdk="Microsoft.NET.Sdk.Razor">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>

    <ItemGroup>
        <SupportedPlatform Include="browser" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.AspNetCore.Components.Web" Version="9.0.0" />
        <PackageReference Include="Microsoft.AspNetCore.Components.Authorization" Version="8.0.0" />
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.0" />
        <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.0.0" />
    </ItemGroup>

</Project>