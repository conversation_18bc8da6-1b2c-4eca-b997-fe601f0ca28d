using System.ComponentModel.DataAnnotations;
using LegendXManager.Shared.DTOs;
using LegendXManager.Shared.Models;

namespace LegendXManager.Shared.Services.Auth;

public interface IAuthService
{
    // Authentication
    Task<LoginResponse> LoginAsync(LoginRequest request);
    Task<RegisterResponse> RegisterAsync(RegisterRequest request);
    Task<RefreshTokenResponse> RefreshTokenAsync(RefreshTokenRequest request);
    Task<bool> LogoutAsync();
    Task<bool> LogoutAllSessionsAsync();
    
    // Password Management
    Task<bool> ChangePasswordAsync(ChangePasswordRequest request);
    Task<bool> ForgotPasswordAsync(ForgotPasswordRequest request);
    Task<bool> ResetPasswordAsync(ResetPasswordRequest request);
    
    // Email Confirmation
    Task<bool> SendEmailConfirmationAsync(string email);
    Task<bool> ConfirmEmailAsync(string email, string token);
    
    // User State
    Task<UserDto?> GetCurrentUserAsync();
    Task<bool> IsAuthenticatedAsync();
    Task<bool> HasPermissionAsync(string permission);
    Task<bool> HasRoleAsync(string role);
    Task<List<string>> GetUserPermissionsAsync();
    Task<List<string>> GetUserRolesAsync();
    
    // Profile Management
    Task<bool> UpdateProfileAsync(UpdateProfileRequest request);
    Task<bool> UploadProfileImageAsync(Stream imageStream, string fileName);
    
    // Session Management
    Task<List<SessionDto>> GetActiveSessionsAsync();
    Task<bool> RevokeSessionAsync(int sessionId);
    Task<bool> RevokeAllOtherSessionsAsync();
    
    // Events
    event EventHandler<UserDto?>? AuthenticationStateChanged;
    event EventHandler<string>? SessionExpiring;
    event EventHandler? SessionExpired;
}

public interface IUserManagementService
{
    // User CRUD
    Task<List<UserDto>> GetUsersAsync(int page = 1, int pageSize = 10, string? search = null);
    Task<UserDto?> GetUserByIdAsync(int userId);
    Task<UserDto?> GetUserByUsernameAsync(string username);
    Task<UserDto?> GetUserByEmailAsync(string email);
    Task<UserDto> CreateUserAsync(CreateUserRequest request);
    Task<UserDto> UpdateUserAsync(int userId, UpdateUserRequest request);
    Task<bool> DeleteUserAsync(int userId);
    Task<bool> ActivateUserAsync(int userId);
    Task<bool> DeactivateUserAsync(int userId);
    
    // User Roles
    Task<List<string>> GetUserRolesAsync(int userId);
    Task<bool> AssignRoleToUserAsync(int userId, int roleId, DateTime? expiresAt = null);
    Task<bool> RemoveRoleFromUserAsync(int userId, int roleId);
    Task<bool> UpdateUserRolesAsync(int userId, List<int> roleIds);
    
    // User Sessions
    Task<List<SessionDto>> GetUserSessionsAsync(int userId);
    Task<bool> RevokeUserSessionAsync(int userId, int sessionId);
    Task<bool> RevokeAllUserSessionsAsync(int userId);
    
    // User Security
    Task<bool> LockUserAsync(int userId, TimeSpan? lockoutDuration = null);
    Task<bool> UnlockUserAsync(int userId);
    Task<bool> ResetUserPasswordAsync(int userId, string newPassword);
    Task<bool> ForcePasswordChangeAsync(int userId);
    
    // Audit
    Task<List<AuditLogDto>> GetUserAuditLogsAsync(int userId, int page = 1, int pageSize = 10);
    Task<List<LoginAttemptDto>> GetUserLoginAttemptsAsync(int userId, int page = 1, int pageSize = 10);
}

public interface IRoleManagementService
{
    // Role CRUD
    Task<List<RoleDto>> GetRolesAsync();
    Task<RoleDto?> GetRoleByIdAsync(int roleId);
    Task<RoleDto?> GetRoleByNameAsync(string roleName);
    Task<RoleDto> CreateRoleAsync(CreateRoleRequest request);
    Task<RoleDto> UpdateRoleAsync(int roleId, UpdateRoleRequest request);
    Task<bool> DeleteRoleAsync(int roleId);
    
    // Role Permissions
    Task<List<PermissionDto>> GetRolePermissionsAsync(int roleId);
    Task<bool> AssignPermissionToRoleAsync(int roleId, int permissionId);
    Task<bool> RemovePermissionFromRoleAsync(int roleId, int permissionId);
    Task<bool> UpdateRolePermissionsAsync(int roleId, List<int> permissionIds);
    
    // Role Users
    Task<List<UserDto>> GetRoleUsersAsync(int roleId);
    Task<int> GetRoleUserCountAsync(int roleId);
    
    // Permissions
    Task<List<PermissionDto>> GetAllPermissionsAsync();
    Task<List<PermissionDto>> GetPermissionsByCategoryAsync(string category);
    Task<List<string>> GetPermissionCategoriesAsync();
}

public interface ISessionManagementService
{
    // Session Management
    Task<List<SessionDto>> GetAllActiveSessionsAsync();
    Task<List<SessionDto>> GetSessionsByUserAsync(int userId);
    Task<SessionDto?> GetSessionByIdAsync(int sessionId);
    Task<bool> RevokeSessionAsync(int sessionId, string? reason = null);
    Task<bool> RevokeAllSessionsAsync(string? reason = null);
    Task<bool> RevokeUserSessionsAsync(int userId, string? reason = null);
    Task<int> CleanupExpiredSessionsAsync();
    
    // Session Analytics
    Task<SessionStatsDto> GetSessionStatsAsync();
    Task<List<ActiveUserDto>> GetActiveUsersAsync();
    Task<Dictionary<string, int>> GetSessionsByDeviceTypeAsync();
    Task<Dictionary<string, int>> GetSessionsByLocationAsync();
}

// Additional DTOs for management services
public class CreateUserRequest : RegisterRequest
{
    public List<int> RoleIds { get; set; } = new();
    public bool IsActive { get; set; } = true;
    public bool RequirePasswordChange { get; set; } = true;
}

public class UpdateUserRequest
{
    public string? FirstName { get; set; }
    public string? LastName { get; set; }
    public string? Email { get; set; }
    public string? PhoneNumber { get; set; }
    public bool? IsActive { get; set; }
    public List<int>? RoleIds { get; set; }
}

public class RoleDto
{
    public int Id { get; set; }
    public string Name { get; set; } = "";
    public string? Description { get; set; }
    public bool IsActive { get; set; }
    public bool IsSystemRole { get; set; }
    public DateTime CreatedAt { get; set; }
    public int UserCount { get; set; }
    public List<string> Permissions { get; set; } = new();
}

public class PermissionDto
{
    public int Id { get; set; }
    public string Name { get; set; } = "";
    public string? Description { get; set; }
    public string Category { get; set; } = "";
    public bool IsActive { get; set; }
}

public class CreateRoleRequest
{
    [Required]
    public string Name { get; set; } = "";
    public string? Description { get; set; }
    public List<int> PermissionIds { get; set; } = new();
}

public class UpdateRoleRequest
{
    public string? Name { get; set; }
    public string? Description { get; set; }
    public bool? IsActive { get; set; }
    public List<int>? PermissionIds { get; set; }
}

public class AuditLogDto
{
    public int Id { get; set; }
    public string Action { get; set; } = "";
    public string? EntityType { get; set; }
    public int? EntityId { get; set; }
    public DateTime Timestamp { get; set; }
    public string? IpAddress { get; set; }
    public string? Details { get; set; }
    public string Severity { get; set; } = "";
    public string? UserName { get; set; }
}

public class LoginAttemptDto
{
    public int Id { get; set; }
    public string Username { get; set; } = "";
    public string? IpAddress { get; set; }
    public bool IsSuccessful { get; set; }
    public string? FailureReason { get; set; }
    public DateTime AttemptedAt { get; set; }
}

public class SessionStatsDto
{
    public int TotalActiveSessions { get; set; }
    public int UniqueActiveUsers { get; set; }
    public int SessionsToday { get; set; }
    public int SessionsThisWeek { get; set; }
    public int SessionsThisMonth { get; set; }
    public double AverageSessionDuration { get; set; }
    public Dictionary<string, int> SessionsByHour { get; set; } = new();
    public Dictionary<string, int> SessionsByDay { get; set; } = new();
}

public class ActiveUserDto
{
    public int UserId { get; set; }
    public string Username { get; set; } = "";
    public string FullName { get; set; } = "";
    public DateTime LastActivityAt { get; set; }
    public int ActiveSessions { get; set; }
    public string? CurrentLocation { get; set; }
    public string? CurrentDevice { get; set; }
}
