using LegendXManager.Shared.DTOs;
using LegendXManager.Shared.Services.Auth;

namespace LegendXManager.Shared.Services.Auth;

public class MockUserManagementService : IUserManagementService
{
    private static List<UserDto> _users = new()
    {
        new UserDto
        {
            Id = 1,
            Username = "admin",
            Email = "<EMAIL>",
            FirstName = "System",
            LastName = "Administrator",
            FullName = "System Administrator",
            IsActive = true,
            IsEmailConfirmed = true,
            CreatedAt = DateTime.UtcNow.AddDays(-30),
            LastLoginAt = DateTime.UtcNow.AddMinutes(-15),
            LastActivityAt = DateTime.UtcNow.AddMinutes(-5),
            Roles = new List<string> { "Admin", "User" },
            Permissions = new List<string> { "UserManagement", "RoleManagement", "SystemSettings" }
        },
        new UserDto
        {
            Id = 2,
            Username = "john.doe",
            Email = "<EMAIL>",
            FirstName = "John",
            LastName = "Doe",
            FullName = "<PERSON> Do<PERSON>",
            IsActive = true,
            IsEmailConfirmed = true,
            CreatedAt = DateTime.UtcNow.AddDays(-15),
            LastLoginAt = DateTime.UtcNow.AddHours(-2),
            LastActivityAt = DateTime.UtcNow.AddMinutes(-30),
            Roles = new List<string> { "User", "Developer" },
            Permissions = new List<string> { "CodeAccess", "ProjectManagement" }
        },
        new UserDto
        {
            Id = 3,
            Username = "jane.smith",
            Email = "<EMAIL>",
            FirstName = "Jane",
            LastName = "Smith",
            FullName = "Jane Smith",
            IsActive = false,
            IsEmailConfirmed = true,
            CreatedAt = DateTime.UtcNow.AddDays(-7),
            LastLoginAt = DateTime.UtcNow.AddDays(-3),
            LastActivityAt = DateTime.UtcNow.AddDays(-3),
            Roles = new List<string> { "User" },
            Permissions = new List<string> { "BasicAccess" }
        },
        new UserDto
        {
            Id = 4,
            Username = "mike.wilson",
            Email = "<EMAIL>",
            FirstName = "Mike",
            LastName = "Wilson",
            FullName = "Mike Wilson",
            IsActive = true,
            IsEmailConfirmed = false,
            CreatedAt = DateTime.UtcNow.AddDays(-2),
            LastLoginAt = null,
            LastActivityAt = null,
            Roles = new List<string> { "User" },
            Permissions = new List<string> { "BasicAccess" }
        }
    };

    public async Task<List<UserDto>> GetUsersAsync(int page = 1, int pageSize = 10, string? search = null)
    {
        await Task.Delay(500); // Simulate API delay

        var query = _users.AsQueryable();

        if (!string.IsNullOrEmpty(search))
        {
            query = query.Where(u => 
                u.Username.Contains(search, StringComparison.OrdinalIgnoreCase) ||
                u.Email.Contains(search, StringComparison.OrdinalIgnoreCase) ||
                u.FullName.Contains(search, StringComparison.OrdinalIgnoreCase));
        }

        return query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToList();
    }

    public async Task<UserDto?> GetUserByIdAsync(int userId)
    {
        await Task.Delay(200);
        return _users.FirstOrDefault(u => u.Id == userId);
    }

    public async Task<UserDto?> GetUserByUsernameAsync(string username)
    {
        await Task.Delay(200);
        return _users.FirstOrDefault(u => u.Username.Equals(username, StringComparison.OrdinalIgnoreCase));
    }

    public async Task<UserDto?> GetUserByEmailAsync(string email)
    {
        await Task.Delay(200);
        return _users.FirstOrDefault(u => u.Email.Equals(email, StringComparison.OrdinalIgnoreCase));
    }

    public async Task<UserDto> CreateUserAsync(CreateUserRequest request)
    {
        await Task.Delay(300);
        
        var newUser = new UserDto
        {
            Id = _users.Max(u => u.Id) + 1,
            Username = request.Username,
            Email = request.Email,
            FirstName = request.FirstName,
            LastName = request.LastName,
            FullName = $"{request.FirstName} {request.LastName}",
            IsActive = request.IsActive,
            IsEmailConfirmed = false,
            CreatedAt = DateTime.UtcNow,
            Roles = new List<string> { "User" },
            Permissions = new List<string> { "BasicAccess" }
        };

        _users.Add(newUser);
        return newUser;
    }

    public async Task<UserDto> UpdateUserAsync(int userId, UpdateUserRequest request)
    {
        await Task.Delay(300);
        
        var user = _users.FirstOrDefault(u => u.Id == userId);
        if (user == null)
            throw new ArgumentException("User not found");

        if (!string.IsNullOrEmpty(request.FirstName))
            user.FirstName = request.FirstName;
        
        if (!string.IsNullOrEmpty(request.LastName))
            user.LastName = request.LastName;
        
        if (!string.IsNullOrEmpty(request.Email))
            user.Email = request.Email;
        
        if (!string.IsNullOrEmpty(request.PhoneNumber))
            user.PhoneNumber = request.PhoneNumber;
        
        if (request.IsActive.HasValue)
            user.IsActive = request.IsActive.Value;

        user.FullName = $"{user.FirstName} {user.LastName}";
        
        return user;
    }

    public async Task<bool> DeleteUserAsync(int userId)
    {
        await Task.Delay(300);
        var user = _users.FirstOrDefault(u => u.Id == userId);
        if (user != null)
        {
            _users.Remove(user);
            return true;
        }
        return false;
    }

    public async Task<bool> ActivateUserAsync(int userId)
    {
        await Task.Delay(200);
        var user = _users.FirstOrDefault(u => u.Id == userId);
        if (user != null)
        {
            user.IsActive = true;
            return true;
        }
        return false;
    }

    public async Task<bool> DeactivateUserAsync(int userId)
    {
        await Task.Delay(200);
        var user = _users.FirstOrDefault(u => u.Id == userId);
        if (user != null)
        {
            user.IsActive = false;
            return true;
        }
        return false;
    }

    public async Task<List<string>> GetUserRolesAsync(int userId)
    {
        await Task.Delay(200);
        var user = _users.FirstOrDefault(u => u.Id == userId);
        return user?.Roles ?? new List<string>();
    }

    public async Task<bool> AssignRoleToUserAsync(int userId, int roleId, DateTime? expiresAt = null)
    {
        await Task.Delay(200);
        // Mock implementation
        return true;
    }

    public async Task<bool> RemoveRoleFromUserAsync(int userId, int roleId)
    {
        await Task.Delay(200);
        // Mock implementation
        return true;
    }

    public async Task<bool> UpdateUserRolesAsync(int userId, List<int> roleIds)
    {
        await Task.Delay(200);
        // Mock implementation
        return true;
    }

    public async Task<List<SessionDto>> GetUserSessionsAsync(int userId)
    {
        await Task.Delay(200);
        // Mock implementation
        return new List<SessionDto>();
    }

    public async Task<bool> RevokeUserSessionAsync(int userId, int sessionId)
    {
        await Task.Delay(200);
        // Mock implementation
        return true;
    }

    public async Task<bool> RevokeAllUserSessionsAsync(int userId)
    {
        await Task.Delay(200);
        // Mock implementation
        return true;
    }

    public async Task<bool> LockUserAsync(int userId, TimeSpan? lockoutDuration = null)
    {
        await Task.Delay(200);
        // Mock implementation
        return true;
    }

    public async Task<bool> UnlockUserAsync(int userId)
    {
        await Task.Delay(200);
        // Mock implementation
        return true;
    }

    public async Task<bool> ResetUserPasswordAsync(int userId, string newPassword)
    {
        await Task.Delay(200);
        // Mock implementation
        return true;
    }

    public async Task<bool> ForcePasswordChangeAsync(int userId)
    {
        await Task.Delay(200);
        // Mock implementation
        return true;
    }

    public async Task<List<AuditLogDto>> GetUserAuditLogsAsync(int userId, int page = 1, int pageSize = 10)
    {
        await Task.Delay(200);
        // Mock implementation
        return new List<AuditLogDto>();
    }

    public async Task<List<LoginAttemptDto>> GetUserLoginAttemptsAsync(int userId, int page = 1, int pageSize = 10)
    {
        await Task.Delay(200);
        // Mock implementation
        return new List<LoginAttemptDto>();
    }
}
