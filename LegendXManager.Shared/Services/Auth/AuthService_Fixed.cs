using System.Net.Http.Json;
using System.Text.Json;
using LegendXManager.Shared.DTOs;
using Microsoft.Extensions.Logging;

namespace LegendXManager.Shared.Services.Auth;

public class AuthService : IAuthService
{
    private readonly HttpClient _httpClient;
    private readonly CustomAuthenticationStateProvider _authStateProvider;
    private readonly ILogger<AuthService>? _logger;

    public event EventHandler<UserDto?>? AuthenticationStateChanged;
    public event EventHandler<string>? SessionExpiring;
    public event EventHandler? SessionExpired;

    public AuthService(
        HttpClient httpClient, 
        CustomAuthenticationStateProvider authStateProvider,
        ILogger<AuthService>? logger = null)
    {
        _httpClient = httpClient;
        _authStateProvider = authStateProvider;
        _logger = logger;

        // Subscribe to auth state provider events
        _authStateProvider.AuthenticationStateChanged += (sender, user) => AuthenticationStateChanged?.Invoke(sender, user);
        _authStateProvider.SessionExpiring += (sender, message) => SessionExpiring?.Invoke(sender, message);
        _authStateProvider.SessionExpired += (sender, args) => SessionExpired?.Invoke(sender, args);
    }

    public async Task<LoginResponse> LoginAsync(LoginRequest request)
    {
        try
        {
            var response = await _httpClient.PostAsJsonAsync("/api/auth/login", request);
            
            if (response.IsSuccessStatusCode)
            {
                var loginResponse = await response.Content.ReadFromJsonAsync<LoginResponse>();
                if (loginResponse != null && loginResponse.Success)
                {
                    await _authStateProvider.LoginAsync(loginResponse);
                    _logger?.LogInformation("User {Username} logged in successfully", request.Username);
                }
                return loginResponse ?? new LoginResponse { Success = false, Message = "Invalid response" };
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                _logger?.LogWarning("Login failed for user {Username}: {Error}", request.Username, errorContent);
                
                return new LoginResponse 
                { 
                    Success = false, 
                    Message = response.StatusCode == System.Net.HttpStatusCode.Unauthorized 
                        ? "Invalid username or password" 
                        : "Login failed. Please try again." 
                };
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error during login for user {Username}", request.Username);
            return new LoginResponse { Success = false, Message = "An error occurred during login" };
        }
    }

    public async Task<RegisterResponse> RegisterAsync(RegisterRequest request)
    {
        try
        {
            var response = await _httpClient.PostAsJsonAsync("/api/auth/register", request);
            
            if (response.IsSuccessStatusCode)
            {
                var registerResponse = await response.Content.ReadFromJsonAsync<RegisterResponse>();
                _logger?.LogInformation("User {Username} registered successfully", request.Username);
                return registerResponse ?? new RegisterResponse { Success = false, Message = "Invalid response" };
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                _logger?.LogWarning("Registration failed for user {Username}: {Error}", request.Username, errorContent);
                
                return new RegisterResponse 
                { 
                    Success = false, 
                    Message = "Registration failed. Please try again." 
                };
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error during registration for user {Username}", request.Username);
            return new RegisterResponse { Success = false, Message = "An error occurred during registration" };
        }
    }

    public async Task<RefreshTokenResponse> RefreshTokenAsync(RefreshTokenRequest request)
    {
        try
        {
            var response = await _httpClient.PostAsJsonAsync("/api/auth/refresh", request);
            
            if (response.IsSuccessStatusCode)
            {
                var refreshResponse = await response.Content.ReadFromJsonAsync<RefreshTokenResponse>();
                return refreshResponse ?? new RefreshTokenResponse { Success = false, Message = "Invalid response" };
            }
            else
            {
                return new RefreshTokenResponse { Success = false, Message = "Token refresh failed" };
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error during token refresh");
            return new RefreshTokenResponse { Success = false, Message = "An error occurred during token refresh" };
        }
    }

    public async Task<bool> LogoutAsync()
    {
        try
        {
            // Call server logout endpoint
            var refreshToken = await _authStateProvider.GetRefreshTokenAsync();
            if (!string.IsNullOrEmpty(refreshToken))
            {
                await _httpClient.PostAsJsonAsync("/api/auth/logout", new { RefreshToken = refreshToken });
            }

            // Clear local state
            await _authStateProvider.LogoutAsync();
            return true;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error during logout");
            // Still clear local state even if server call fails
            await _authStateProvider.LogoutAsync();
            return false;
        }
    }

    public async Task<bool> LogoutAllSessionsAsync()
    {
        try
        {
            var response = await _httpClient.PostAsync("/api/auth/logout-all", null);
            if (response.IsSuccessStatusCode)
            {
                await _authStateProvider.LogoutAsync();
                return true;
            }
            return false;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error during logout all sessions");
            return false;
        }
    }

    public async Task<bool> ChangePasswordAsync(ChangePasswordRequest request)
    {
        try
        {
            var response = await _httpClient.PostAsJsonAsync("/api/auth/change-password", request);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error during password change");
            return false;
        }
    }

    public async Task<bool> ForgotPasswordAsync(ForgotPasswordRequest request)
    {
        try
        {
            var response = await _httpClient.PostAsJsonAsync("/api/auth/forgot-password", request);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error during forgot password for email {Email}", request.Email);
            return false;
        }
    }

    public async Task<bool> ResetPasswordAsync(ResetPasswordRequest request)
    {
        try
        {
            var response = await _httpClient.PostAsJsonAsync("/api/auth/reset-password", request);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error during password reset for email {Email}", request.Email);
            return false;
        }
    }

    public async Task<bool> SendEmailConfirmationAsync(string email)
    {
        try
        {
            var response = await _httpClient.PostAsJsonAsync("/api/auth/send-email-confirmation", new { Email = email });
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error sending email confirmation for {Email}", email);
            return false;
        }
    }

    public async Task<bool> ConfirmEmailAsync(string email, string token)
    {
        try
        {
            var response = await _httpClient.PostAsJsonAsync("/api/auth/confirm-email", new { Email = email, Token = token });
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error confirming email for {Email}", email);
            return false;
        }
    }

    public Task<UserDto?> GetCurrentUserAsync()
    {
        return Task.FromResult(_authStateProvider.GetCurrentUser());
    }

    public Task<bool> IsAuthenticatedAsync()
    {
        return Task.FromResult(_authStateProvider.IsAuthenticated());
    }

    public Task<bool> HasPermissionAsync(string permission)
    {
        return Task.FromResult(_authStateProvider.HasPermission(permission));
    }

    public Task<bool> HasRoleAsync(string role)
    {
        return Task.FromResult(_authStateProvider.HasRole(role));
    }

    public Task<List<string>> GetUserPermissionsAsync()
    {
        var user = _authStateProvider.GetCurrentUser();
        return Task.FromResult(user?.Permissions ?? new List<string>());
    }

    public Task<List<string>> GetUserRolesAsync()
    {
        var user = _authStateProvider.GetCurrentUser();
        return Task.FromResult(user?.Roles ?? new List<string>());
    }

    public async Task<bool> UpdateProfileAsync(UpdateProfileRequest request)
    {
        try
        {
            var response = await _httpClient.PutAsJsonAsync("/api/auth/profile", request);
            
            if (response.IsSuccessStatusCode)
            {
                // Update local user data
                var updatedUser = await response.Content.ReadFromJsonAsync<UserDto>();
                if (updatedUser != null)
                {
                    await _authStateProvider.UpdateUserAsync(updatedUser);
                }
                return true;
            }
            return false;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error updating profile");
            return false;
        }
    }

    public async Task<bool> UploadProfileImageAsync(Stream imageStream, string fileName)
    {
        try
        {
            using var content = new MultipartFormDataContent();
            using var streamContent = new StreamContent(imageStream);
            streamContent.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("image/jpeg");
            content.Add(streamContent, "file", fileName);

            var response = await _httpClient.PostAsync("/api/auth/profile/image", content);
            
            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<dynamic>();
                // Update user's profile image URL
                var currentUser = _authStateProvider.GetCurrentUser();
                if (currentUser != null)
                {
                    // In a real app, you'd get the new image URL from the response
                    currentUser.ProfileImageUrl = "/api/auth/profile/image"; // Placeholder
                    await _authStateProvider.UpdateUserAsync(currentUser);
                }
                return true;
            }
            return false;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error uploading profile image");
            return false;
        }
    }

    public async Task<List<SessionDto>> GetActiveSessionsAsync()
    {
        try
        {
            var response = await _httpClient.GetAsync("/api/auth/sessions");
            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<SessionDto>>() ?? new List<SessionDto>();
            }
            return new List<SessionDto>();
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error getting active sessions");
            return new List<SessionDto>();
        }
    }

    public async Task<bool> RevokeSessionAsync(int sessionId)
    {
        try
        {
            var response = await _httpClient.DeleteAsync($"/api/auth/sessions/{sessionId}");
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error revoking session {SessionId}", sessionId);
            return false;
        }
    }

    public async Task<bool> RevokeAllOtherSessionsAsync()
    {
        try
        {
            var response = await _httpClient.DeleteAsync("/api/auth/sessions/others");
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error revoking other sessions");
            return false;
        }
    }
}
