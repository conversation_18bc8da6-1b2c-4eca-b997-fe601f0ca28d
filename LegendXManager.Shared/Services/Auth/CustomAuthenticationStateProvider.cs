using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.JSInterop;
using System.Security.Claims;
using System.Text.Json;
using LegendXManager.Shared.DTOs;
using Microsoft.Extensions.Logging;

namespace LegendXManager.Shared.Services.Auth;

public class CustomAuthenticationStateProvider : AuthenticationStateProvider
{
    private readonly IJSRuntime _jsRuntime;
    private readonly ILogger<CustomAuthenticationStateProvider>? _logger;
    private UserDto? _currentUser;
    private Timer? _sessionTimer;
    private Timer? _refreshTimer;
    private DateTime _tokenExpiry;
    private bool _isInitialized = false;

    public event EventHandler<UserDto?>? AuthenticationStateChanged;
    public event EventHandler<string>? SessionExpiring;
    public event EventHandler? SessionExpired;

    public CustomAuthenticationStateProvider(IJSRuntime jsRuntime, ILogger<CustomAuthenticationStateProvider>? logger = null)
    {
        _jsRuntime = jsRuntime;
        _logger = logger;
    }

    public override async Task<AuthenticationState> GetAuthenticationStateAsync()
    {
        if (!_isInitialized)
        {
            await InitializeAsync();
        }

        if (_currentUser == null)
        {
            return new AuthenticationState(new ClaimsPrincipal(new ClaimsIdentity()));
        }

        var claims = CreateClaims(_currentUser);
        var identity = new ClaimsIdentity(claims, "jwt");
        var principal = new ClaimsPrincipal(identity);

        return new AuthenticationState(principal);
    }

    public async Task<bool> LoginAsync(LoginResponse loginResponse)
    {
        try
        {
            if (!loginResponse.Success || loginResponse.User == null)
            {
                return false;
            }

            // Store tokens
            if (!string.IsNullOrEmpty(loginResponse.AccessToken))
            {
                await _jsRuntime.InvokeVoidAsync("localStorage.setItem", "accessToken", loginResponse.AccessToken);
            }

            if (!string.IsNullOrEmpty(loginResponse.RefreshToken))
            {
                await _jsRuntime.InvokeVoidAsync("localStorage.setItem", "refreshToken", loginResponse.RefreshToken);
            }

            // Store user data
            var userJson = JsonSerializer.Serialize(loginResponse.User);
            await _jsRuntime.InvokeVoidAsync("localStorage.setItem", "currentUser", userJson);

            // Update state
            _currentUser = loginResponse.User;
            _tokenExpiry = loginResponse.ExpiresAt ?? DateTime.UtcNow.AddHours(1);

            // Start session management
            StartSessionManagement();

            // Notify state change
            NotifyAuthenticationStateChanged();
            AuthenticationStateChanged?.Invoke(this, _currentUser);

            _logger?.LogInformation("User {Username} logged in successfully", _currentUser.Username);
            return true;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error during login");
            return false;
        }
    }

    public async Task LogoutAsync()
    {
        try
        {
            // Clear tokens and user data
            await _jsRuntime.InvokeVoidAsync("localStorage.removeItem", "accessToken");
            await _jsRuntime.InvokeVoidAsync("localStorage.removeItem", "refreshToken");
            await _jsRuntime.InvokeVoidAsync("localStorage.removeItem", "currentUser");

            // Stop timers
            StopSessionManagement();

            // Clear state
            var previousUser = _currentUser;
            _currentUser = null;

            // Notify state change
            NotifyAuthenticationStateChanged();
            AuthenticationStateChanged?.Invoke(this, null);

            _logger?.LogInformation("User {Username} logged out", previousUser?.Username);
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error during logout");
        }
    }

    public async Task<string?> GetAccessTokenAsync()
    {
        try
        {
            return await _jsRuntime.InvokeAsync<string?>("localStorage.getItem", "accessToken");
        }
        catch
        {
            return null;
        }
    }

    public async Task<string?> GetRefreshTokenAsync()
    {
        try
        {
            return await _jsRuntime.InvokeAsync<string?>("localStorage.getItem", "refreshToken");
        }
        catch
        {
            return null;
        }
    }

    public UserDto? GetCurrentUser()
    {
        return _currentUser;
    }

    public bool IsAuthenticated()
    {
        return _currentUser != null;
    }

    public bool HasPermission(string permission)
    {
        return _currentUser?.Permissions.Contains(permission) ?? false;
    }

    public bool HasRole(string role)
    {
        return _currentUser?.Roles.Contains(role) ?? false;
    }

    public async Task UpdateUserAsync(UserDto user)
    {
        _currentUser = user;
        var userJson = JsonSerializer.Serialize(user);
        await _jsRuntime.InvokeVoidAsync("localStorage.setItem", "currentUser", userJson);
        NotifyAuthenticationStateChanged();
        AuthenticationStateChanged?.Invoke(this, _currentUser);
    }

    private async Task InitializeAsync()
    {
        try
        {
            // Try to load user from localStorage
            var userJson = await _jsRuntime.InvokeAsync<string?>("localStorage.getItem", "currentUser");
            if (!string.IsNullOrEmpty(userJson))
            {
                _currentUser = JsonSerializer.Deserialize<UserDto>(userJson);
                
                // Check if we have a valid token
                var accessToken = await GetAccessTokenAsync();
                if (!string.IsNullOrEmpty(accessToken))
                {
                    // Parse token expiry (in a real app, you'd decode the JWT)
                    _tokenExpiry = DateTime.UtcNow.AddHours(1); // Default expiry
                    StartSessionManagement();
                }
                else
                {
                    // No valid token, clear user
                    _currentUser = null;
                    await _jsRuntime.InvokeVoidAsync("localStorage.removeItem", "currentUser");
                }
            }

            _isInitialized = true;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error initializing authentication state");
            _isInitialized = true;
        }
    }

    private void StartSessionManagement()
    {
        StopSessionManagement();

        // Timer to check for session expiry (check every minute)
        _sessionTimer = new Timer(CheckSessionExpiry, null, TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));

        // Timer to refresh token (refresh 5 minutes before expiry)
        var refreshTime = _tokenExpiry.AddMinutes(-5) - DateTime.UtcNow;
        if (refreshTime > TimeSpan.Zero)
        {
            _refreshTimer = new Timer(async _ => await TryRefreshTokenAsync(), null, refreshTime, Timeout.InfiniteTimeSpan);
        }
    }

    private void StopSessionManagement()
    {
        _sessionTimer?.Dispose();
        _refreshTimer?.Dispose();
        _sessionTimer = null;
        _refreshTimer = null;
    }

    private void CheckSessionExpiry(object? state)
    {
        var timeUntilExpiry = _tokenExpiry - DateTime.UtcNow;
        
        if (timeUntilExpiry <= TimeSpan.Zero)
        {
            // Session expired
            _ = Task.Run(async () =>
            {
                await LogoutAsync();
                SessionExpired?.Invoke(this, EventArgs.Empty);
            });
        }
        else if (timeUntilExpiry <= TimeSpan.FromMinutes(5))
        {
            // Session expiring soon
            SessionExpiring?.Invoke(this, $"Your session will expire in {timeUntilExpiry.Minutes} minutes");
        }
    }

    private async Task TryRefreshTokenAsync()
    {
        try
        {
            var refreshToken = await GetRefreshTokenAsync();
            if (string.IsNullOrEmpty(refreshToken))
            {
                await LogoutAsync();
                return;
            }

            // In a real app, you'd call your refresh token endpoint here
            // For now, we'll just extend the session
            _tokenExpiry = DateTime.UtcNow.AddHours(1);
            StartSessionManagement();
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error refreshing token");
            await LogoutAsync();
        }
    }

    private static List<Claim> CreateClaims(UserDto user)
    {
        var claims = new List<Claim>
        {
            new(ClaimTypes.NameIdentifier, user.Id.ToString()),
            new(ClaimTypes.Name, user.Username),
            new(ClaimTypes.Email, user.Email),
            new(ClaimTypes.GivenName, user.FirstName),
            new(ClaimTypes.Surname, user.LastName),
            new("FullName", user.FullName),
            new("IsEmailConfirmed", user.IsEmailConfirmed.ToString()),
            new("Theme", user.Theme ?? "dark"),
            new("Language", user.Language ?? "en")
        };

        // Add roles
        foreach (var role in user.Roles)
        {
            claims.Add(new Claim(ClaimTypes.Role, role));
        }

        // Add permissions
        foreach (var permission in user.Permissions)
        {
            claims.Add(new Claim("permission", permission));
        }

        return claims;
    }

    public override void Dispose()
    {
        StopSessionManagement();
        base.Dispose();
    }
}
