using System.ComponentModel.DataAnnotations;

namespace LegendXManager.Shared.Models;

public class User
{
    public int Id { get; set; }
    
    [Required]
    [StringLength(50)]
    public string Username { get; set; } = "";
    
    [Required]
    [EmailAddress]
    [StringLength(100)]
    public string Email { get; set; } = "";
    
    [Required]
    [StringLength(100)]
    public string FirstName { get; set; } = "";
    
    [Required]
    [StringLength(100)]
    public string LastName { get; set; } = "";
    
    public string PasswordHash { get; set; } = "";
    
    public bool IsActive { get; set; } = true;
    
    public bool IsEmailConfirmed { get; set; } = false;
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime? LastLoginAt { get; set; }
    
    public DateTime? LastActivityAt { get; set; }
    
    public string? RefreshToken { get; set; }
    
    public DateTime? RefreshTokenExpiryTime { get; set; }
    
    public string? EmailConfirmationToken { get; set; }
    
    public string? PasswordResetToken { get; set; }
    
    public DateTime? PasswordResetTokenExpiry { get; set; }
    
    public int FailedLoginAttempts { get; set; } = 0;
    
    public DateTime? LockoutEnd { get; set; }
    
    public string? ProfileImageUrl { get; set; }
    
    public string? PhoneNumber { get; set; }
    
    public string? TimeZone { get; set; }
    
    public string? Language { get; set; } = "en";
    
    public string? Theme { get; set; } = "dark";
    
    // Navigation properties
    public virtual ICollection<UserRole> UserRoles { get; set; } = new List<UserRole>();
    public virtual ICollection<UserSession> UserSessions { get; set; } = new List<UserSession>();
    public virtual ICollection<AuditLog> AuditLogs { get; set; } = new List<AuditLog>();
    
    // Computed properties
    public string FullName => $"{FirstName} {LastName}".Trim();
    
    public bool IsLocked => LockoutEnd.HasValue && LockoutEnd > DateTime.UtcNow;
    
    public List<string> Roles => UserRoles.Select(ur => ur.Role.Name).ToList();
    
    public List<string> Permissions => UserRoles
        .SelectMany(ur => ur.Role.RolePermissions)
        .Select(rp => rp.Permission.Name)
        .Distinct()
        .ToList();
}
