using System.ComponentModel.DataAnnotations;

namespace LegendXManager.Shared.Models;

public class UserSession
{
    public int Id { get; set; }
    
    public int UserId { get; set; }
    
    [Required]
    [StringLength(500)]
    public string SessionToken { get; set; } = "";
    
    [Required]
    [StringLength(500)]
    public string RefreshToken { get; set; } = "";
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime ExpiresAt { get; set; }
    
    public DateTime RefreshTokenExpiresAt { get; set; }
    
    public DateTime? LastActivityAt { get; set; }
    
    public bool IsActive { get; set; } = true;
    
    [StringLength(45)]
    public string? IpAddress { get; set; }
    
    [StringLength(500)]
    public string? UserAgent { get; set; }
    
    [StringLength(100)]
    public string? DeviceType { get; set; }
    
    [StringLength(100)]
    public string? Platform { get; set; }
    
    [StringLength(100)]
    public string? Browser { get; set; }
    
    [StringLength(100)]
    public string? Location { get; set; }
    
    public DateTime? RevokedAt { get; set; }
    
    public int? RevokedByUserId { get; set; }
    
    [StringLength(200)]
    public string? RevokedReason { get; set; }
    
    // Navigation properties
    public virtual User User { get; set; } = null!;
    public virtual User? RevokedByUser { get; set; }
    
    // Computed properties
    public bool IsExpired => DateTime.UtcNow > ExpiresAt;
    public bool IsRefreshTokenExpired => DateTime.UtcNow > RefreshTokenExpiresAt;
    public bool IsRevoked => RevokedAt.HasValue;
    public bool IsValid => IsActive && !IsExpired && !IsRevoked;
    
    public TimeSpan TimeUntilExpiry => ExpiresAt - DateTime.UtcNow;
    public TimeSpan TimeSinceLastActivity => LastActivityAt.HasValue ? DateTime.UtcNow - LastActivityAt.Value : TimeSpan.Zero;
}

public class AuditLog
{
    public int Id { get; set; }
    
    public int? UserId { get; set; }
    
    [Required]
    [StringLength(100)]
    public string Action { get; set; } = "";
    
    [StringLength(100)]
    public string? EntityType { get; set; }
    
    public int? EntityId { get; set; }
    
    public string? OldValues { get; set; }
    
    public string? NewValues { get; set; }
    
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    
    [StringLength(45)]
    public string? IpAddress { get; set; }
    
    [StringLength(500)]
    public string? UserAgent { get; set; }
    
    [StringLength(200)]
    public string? Details { get; set; }
    
    [StringLength(50)]
    public string Severity { get; set; } = "Info"; // Info, Warning, Error, Critical
    
    // Navigation properties
    public virtual User? User { get; set; }
}

public class LoginAttempt
{
    public int Id { get; set; }
    
    [Required]
    [StringLength(100)]
    public string Username { get; set; } = "";
    
    [StringLength(45)]
    public string? IpAddress { get; set; }
    
    [StringLength(500)]
    public string? UserAgent { get; set; }
    
    public bool IsSuccessful { get; set; }
    
    [StringLength(200)]
    public string? FailureReason { get; set; }
    
    public DateTime AttemptedAt { get; set; } = DateTime.UtcNow;
    
    public int? UserId { get; set; }
    
    // Navigation properties
    public virtual User? User { get; set; }
}
