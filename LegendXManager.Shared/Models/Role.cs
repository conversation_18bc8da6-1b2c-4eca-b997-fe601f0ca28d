using System.ComponentModel.DataAnnotations;

namespace LegendXManager.Shared.Models;

public class Role
{
    public int Id { get; set; }
    
    [Required]
    [StringLength(50)]
    public string Name { get; set; } = "";
    
    [StringLength(200)]
    public string? Description { get; set; }
    
    public bool IsActive { get; set; } = true;
    
    public bool IsSystemRole { get; set; } = false;
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime? UpdatedAt { get; set; }
    
    public int? CreatedByUserId { get; set; }
    
    public int? UpdatedByUserId { get; set; }
    
    // Navigation properties
    public virtual ICollection<UserRole> UserRoles { get; set; } = new List<UserRole>();
    public virtual ICollection<RolePermission> RolePermissions { get; set; } = new List<RolePermission>();
    public virtual User? CreatedByUser { get; set; }
    public virtual User? UpdatedByUser { get; set; }
    
    // Computed properties
    public List<string> Permissions => RolePermissions.Select(rp => rp.Permission.Name).ToList();
    public int UserCount => UserRoles.Count;
}

public class Permission
{
    public int Id { get; set; }
    
    [Required]
    [StringLength(100)]
    public string Name { get; set; } = "";
    
    [StringLength(200)]
    public string? Description { get; set; }
    
    [StringLength(50)]
    public string Category { get; set; } = "";
    
    public bool IsActive { get; set; } = true;
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    // Navigation properties
    public virtual ICollection<RolePermission> RolePermissions { get; set; } = new List<RolePermission>();
}

public class UserRole
{
    public int Id { get; set; }
    
    public int UserId { get; set; }
    
    public int RoleId { get; set; }
    
    public DateTime AssignedAt { get; set; } = DateTime.UtcNow;
    
    public int? AssignedByUserId { get; set; }
    
    public DateTime? ExpiresAt { get; set; }
    
    public bool IsActive { get; set; } = true;
    
    // Navigation properties
    public virtual User User { get; set; } = null!;
    public virtual Role Role { get; set; } = null!;
    public virtual User? AssignedByUser { get; set; }
}

public class RolePermission
{
    public int Id { get; set; }
    
    public int RoleId { get; set; }
    
    public int PermissionId { get; set; }
    
    public DateTime GrantedAt { get; set; } = DateTime.UtcNow;
    
    public int? GrantedByUserId { get; set; }
    
    // Navigation properties
    public virtual Role Role { get; set; } = null!;
    public virtual Permission Permission { get; set; } = null!;
    public virtual User? GrantedByUser { get; set; }
}
