/* IDE Layout */
.ide-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: #1e1e1e;
    color: #d4d4d4;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

.ide-header {
    background-color: #2d2d30;
    border-bottom: 1px solid #3e3e42;
    padding: 8px 16px;
}

.ide-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.toolbar-section {
    display: flex;
    gap: 8px;
}

.ide-body {
    flex: 1;
    display: flex;
    overflow: hidden;
}

.ide-sidebar {
    width: 300px;
    background-color: #252526;
    border-right: 1px solid #3e3e42;
    overflow-y: auto;
}

.ide-main {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.ide-footer {
    background-color: #007acc;
    color: white;
    height: 24px;
}

/* File Explorer */
.file-explorer {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.file-explorer-header {
    padding: 12px 16px;
    background-color: #2d2d30;
    border-bottom: 1px solid #3e3e42;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.file-explorer-header h6 {
    color: #cccccc;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 11px;
    letter-spacing: 0.5px;
}

.file-explorer-actions {
    display: flex;
    gap: 4px;
}

.file-explorer-actions .btn {
    padding: 4px 6px;
    font-size: 12px;
    border: none;
    background: transparent;
    color: #cccccc;
}

.file-explorer-actions .btn:hover {
    background-color: #3e3e42;
    color: white;
}

.file-tree {
    flex: 1;
    padding: 8px 0;
    overflow-y: auto;
}

.file-tree-node {
    user-select: none;
}

.folder-item, .file-item {
    display: flex;
    align-items: center;
    padding: 4px 8px;
    cursor: pointer;
    font-size: 13px;
}

.folder-item:hover, .file-item:hover {
    background-color: #2a2d2e;
}

.folder-icon, .file-icon {
    margin-right: 6px;
    width: 16px;
    font-size: 14px;
}

.folder-icon {
    color: #dcb67a;
}

.file-icon {
    color: #519aba;
}

.node-name {
    color: #cccccc;
}

.loading-files {
    padding: 20px;
    text-align: center;
    color: #888;
}

/* Editor */
.editor-container {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.editor-tabs {
    display: flex;
    background-color: #2d2d30;
    border-bottom: 1px solid #3e3e42;
    overflow-x: auto;
}

.editor-tab {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background-color: #2d2d30;
    border-right: 1px solid #3e3e42;
    cursor: pointer;
    font-size: 13px;
    color: #969696;
    min-width: 120px;
    position: relative;
}

.editor-tab:hover {
    background-color: #1e1e1e;
    color: #cccccc;
}

.editor-tab.active {
    background-color: #1e1e1e;
    color: #ffffff;
    border-bottom: 2px solid #007acc;
}

.tab-title {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.tab-close {
    background: none;
    border: none;
    color: inherit;
    padding: 0;
    margin-left: 8px;
    cursor: pointer;
    opacity: 0.7;
}

.tab-close:hover {
    opacity: 1;
}

.no-tabs {
    padding: 12px 16px;
    color: #888;
    font-style: italic;
}

.editor-content {
    flex: 1;
    position: relative;
}

.welcome-screen {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    background-color: #1e1e1e;
}

.welcome-content {
    text-align: center;
    max-width: 400px;
}

.welcome-content h3 {
    color: #cccccc;
    margin-bottom: 16px;
}

.welcome-content p {
    color: #888;
    margin-bottom: 24px;
}

.welcome-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
}

/* Code Editor */
.code-editor {
    height: 100%;
    position: relative;
}

.editor-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    background-color: #1e1e1e;
}

.loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    color: #888;
}

.loading-spinner i {
    font-size: 24px;
}

.monaco-editor-container {
    height: 100%;
}

/* Terminal */
.terminal-container {
    height: 300px;
    background-color: #1e1e1e;
    border-top: 1px solid #3e3e42;
    display: flex;
    flex-direction: column;
}

.terminal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
    background-color: #2d2d30;
    border-bottom: 1px solid #3e3e42;
}

.terminal-title {
    font-size: 13px;
    color: #cccccc;
    font-weight: 600;
}

.terminal {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 12px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 14px;
    overflow: hidden;
}

.terminal-output {
    flex: 1;
    overflow-y: auto;
    margin-bottom: 8px;
}

.terminal-line {
    display: flex;
    margin-bottom: 2px;
    line-height: 1.4;
}

.terminal-line.command {
    color: #569cd6;
}

.terminal-line.error {
    color: #f44747;
}

.terminal-line.success {
    color: #4ec9b0;
}

.terminal-line.info {
    color: #dcdcaa;
}

.terminal-prompt {
    color: #4ec9b0;
    margin-right: 8px;
    font-weight: bold;
}

.terminal-text {
    color: #d4d4d4;
    flex: 1;
}

.terminal-input-container {
    display: flex;
    align-items: center;
}

.terminal-input {
    flex: 1;
    background: transparent;
    border: none;
    color: #d4d4d4;
    font-family: inherit;
    font-size: inherit;
    outline: none;
    padding: 0;
    margin-left: 8px;
}

/* Status Bar */
.status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 16px;
    font-size: 12px;
    height: 24px;
}

.status-left, .status-center, .status-right {
    display: flex;
    align-items: center;
    gap: 16px;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 4px;
    color: rgba(255, 255, 255, 0.8);
}

.status-item.clickable {
    cursor: pointer;
}

.status-item.clickable:hover {
    color: white;
}

.status-item i {
    font-size: 11px;
}

/* Language Selector */
.language-selector-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.language-selector {
    background-color: #2d2d30;
    border: 1px solid #3e3e42;
    border-radius: 4px;
    width: 300px;
    max-height: 400px;
    display: flex;
    flex-direction: column;
}

.language-selector-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #3e3e42;
}

.language-selector-header h6 {
    margin: 0;
    color: #cccccc;
}

.language-list {
    flex: 1;
    overflow-y: auto;
    max-height: 300px;
}

.language-item {
    padding: 8px 16px;
    cursor: pointer;
    color: #cccccc;
    font-size: 13px;
}

.language-item:hover {
    background-color: #3e3e42;
}

.language-item.active {
    background-color: #007acc;
    color: white;
}

/* Animations */
.spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Responsive */
@media (max-width: 768px) {
    .ide-sidebar {
        width: 250px;
    }
    
    .toolbar-section {
        gap: 4px;
    }
    
    .terminal-container {
        height: 200px;
    }
}
