/* Authentication Styles */
.login-container, .register-container, .forgot-password-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 20px;
}

.login-card, .register-card, .forgot-password-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    padding: 40px;
    width: 100%;
    max-width: 420px;
    position: relative;
    overflow: hidden;
}

.login-card::before, .register-card::before, .forgot-password-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
}

.login-header, .register-header, .forgot-password-header {
    text-align: center;
    margin-bottom: 32px;
}

.login-title, .register-title, .forgot-password-title {
    color: #2d3748;
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
}

.login-title i, .register-title i, .forgot-password-title i {
    color: #667eea;
    font-size: 32px;
}

.login-subtitle, .register-subtitle, .forgot-password-subtitle {
    color: #718096;
    font-size: 16px;
    margin: 0;
}

.login-form, .register-form, .forgot-password-form {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-label {
    color: #2d3748;
    font-weight: 600;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.form-label i {
    color: #667eea;
    font-size: 16px;
}

.form-control {
    padding: 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.2s ease;
    background-color: #f7fafc;
}

.form-control:focus {
    outline: none;
    border-color: #667eea;
    background-color: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-control:disabled {
    background-color: #f1f5f9;
    color: #94a3b8;
    cursor: not-allowed;
}

.password-input-container {
    position: relative;
    display: flex;
    align-items: center;
}

.password-toggle-btn {
    position: absolute;
    right: 12px;
    background: none;
    border: none;
    color: #718096;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: color 0.2s ease;
}

.password-toggle-btn:hover {
    color: #667eea;
}

.password-toggle-btn:disabled {
    cursor: not-allowed;
    opacity: 0.5;
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 8px 0;
}

.form-check {
    display: flex;
    align-items: center;
    gap: 8px;
}

.form-check-input {
    width: 18px;
    height: 18px;
    accent-color: #667eea;
}

.form-check-label {
    color: #4a5568;
    font-size: 14px;
    cursor: pointer;
}

.forgot-password-link {
    color: #667eea;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: color 0.2s ease;
}

.forgot-password-link:hover {
    color: #5a67d8;
    text-decoration: underline;
}

.btn-login, .btn-register, .btn-reset {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 14px 24px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-top: 8px;
}

.btn-login:hover, .btn-register:hover, .btn-reset:hover {
    transform: translateY(-1px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

.btn-login:disabled, .btn-register:disabled, .btn-reset:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.login-footer, .register-footer, .forgot-password-footer {
    text-align: center;
    margin-top: 32px;
    padding-top: 24px;
    border-top: 1px solid #e2e8f0;
}

.register-link, .login-link, .back-to-login {
    color: #718096;
    font-size: 14px;
    margin: 0;
}

.link-primary {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.2s ease;
}

.link-primary:hover {
    color: #5a67d8;
    text-decoration: underline;
}

.alert {
    padding: 12px 16px;
    border-radius: 8px;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
}

.alert-danger {
    background-color: #fed7d7;
    color: #c53030;
    border: 1px solid #feb2b2;
}

.alert-success {
    background-color: #c6f6d5;
    color: #2f855a;
    border: 1px solid #9ae6b4;
}

.alert-warning {
    background-color: #fefcbf;
    color: #d69e2e;
    border: 1px solid #faf089;
}

.alert-info {
    background-color: #bee3f8;
    color: #2b6cb0;
    border: 1px solid #90cdf4;
}

.validation-message {
    color: #e53e3e;
    font-size: 12px;
    margin-top: 4px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.validation-message::before {
    content: '⚠';
    font-size: 14px;
}

.spinner-border {
    width: 1rem;
    height: 1rem;
    border: 0.125em solid currentColor;
    border-right-color: transparent;
    border-radius: 50%;
    animation: spinner-border 0.75s linear infinite;
}

.spinner-border-sm {
    width: 0.875rem;
    height: 0.875rem;
    border-width: 0.125em;
}

@keyframes spinner-border {
    to {
        transform: rotate(360deg);
    }
}

/* Session Management */
.session-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    margin-bottom: 16px;
    border-left: 4px solid #667eea;
}

.session-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.session-device {
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 600;
    color: #2d3748;
}

.session-device i {
    font-size: 20px;
    color: #667eea;
}

.session-current {
    background: #48bb78;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.session-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
    margin-bottom: 16px;
}

.session-detail {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.session-detail-label {
    font-size: 12px;
    color: #718096;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.session-detail-value {
    color: #2d3748;
    font-size: 14px;
}

.session-actions {
    display: flex;
    gap: 8px;
}

.btn-revoke {
    background: #e53e3e;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.btn-revoke:hover {
    background: #c53030;
}

/* Responsive Design */
@media (max-width: 480px) {
    .login-container, .register-container, .forgot-password-container {
        padding: 16px;
    }
    
    .login-card, .register-card, .forgot-password-card {
        padding: 24px;
    }
    
    .login-title, .register-title, .forgot-password-title {
        font-size: 24px;
    }
    
    .form-options {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }
    
    .session-details {
        grid-template-columns: 1fr;
    }
    
    .session-actions {
        flex-direction: column;
    }
}
