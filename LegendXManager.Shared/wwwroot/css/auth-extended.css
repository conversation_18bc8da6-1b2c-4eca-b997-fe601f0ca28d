/* Profile Management */
.profile-management {
    max-width: 1000px;
    margin: 0 auto;
    padding: 24px;
}

.profile-header {
    display: flex;
    align-items: center;
    gap: 24px;
    margin-bottom: 32px;
    padding: 24px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.profile-avatar-section {
    position: relative;
}

.profile-avatar {
    position: relative;
    width: 120px;
    height: 120px;
    border-radius: 50%;
    overflow: hidden;
    border: 4px solid #667eea;
}

.avatar-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 36px;
    font-weight: 700;
}

.avatar-upload-btn {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: #667eea;
    color: white;
    border: 3px solid white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s ease;
}

.avatar-upload-btn:hover {
    background: #5a67d8;
}

.profile-info {
    flex: 1;
}

.profile-name {
    font-size: 28px;
    font-weight: 700;
    color: #2d3748;
    margin: 0 0 8px 0;
}

.profile-username {
    font-size: 16px;
    color: #667eea;
    margin: 0 0 4px 0;
    font-weight: 600;
}

.profile-email {
    font-size: 14px;
    color: #718096;
    margin: 0 0 16px 0;
}

.profile-badges {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.role-badge {
    background: #667eea;
    color: white;
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.profile-tabs {
    display: flex;
    background: white;
    border-radius: 12px;
    padding: 8px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow-x: auto;
}

.tab-button {
    flex: 1;
    padding: 12px 16px;
    border: none;
    background: transparent;
    color: #718096;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-weight: 500;
    white-space: nowrap;
}

.tab-button:hover {
    background: #f7fafc;
    color: #2d3748;
}

.tab-button.active {
    background: #667eea;
    color: white;
}

.profile-content {
    background: white;
    border-radius: 12px;
    padding: 32px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.section-title {
    font-size: 20px;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 24px;
    padding-bottom: 12px;
    border-bottom: 2px solid #e2e8f0;
}

.profile-form, .password-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-actions {
    margin-top: 16px;
}

.preferences-section {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.preference-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.preference-label {
    font-weight: 600;
    color: #2d3748;
    font-size: 14px;
}

.form-select {
    padding: 10px 12px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    background: white;
    color: #2d3748;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.form-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* User Management */
.user-management {
    padding: 24px;
    max-width: 1400px;
    margin: 0 auto;
}

.user-management-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 32px;
    padding: 24px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.header-content h2 {
    font-size: 28px;
    font-weight: 700;
    color: #2d3748;
    margin: 0 0 8px 0;
    display: flex;
    align-items: center;
    gap: 12px;
}

.header-content h2 i {
    color: #667eea;
}

.page-subtitle {
    color: #718096;
    margin: 0;
}

.header-actions {
    display: flex;
    gap: 12px;
}

.user-filters {
    display: flex;
    gap: 16px;
    margin-bottom: 24px;
    padding: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    align-items: center;
    flex-wrap: wrap;
}

.search-container {
    flex: 1;
    min-width: 300px;
}

.search-input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.search-icon {
    position: absolute;
    left: 12px;
    color: #718096;
    z-index: 1;
}

.search-input {
    padding-left: 40px;
    padding-right: 40px;
}

.btn-clear {
    position: absolute;
    right: 8px;
    background: none;
    border: none;
    color: #718096;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
}

.btn-clear:hover {
    background: #f7fafc;
    color: #2d3748;
}

.filter-controls {
    display: flex;
    gap: 12px;
}

.users-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
    margin-bottom: 32px;
}

.user-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #667eea;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.user-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.user-card.inactive {
    opacity: 0.7;
    border-left-color: #cbd5e0;
}

.user-avatar {
    position: relative;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    margin-bottom: 16px;
}

.user-status {
    position: absolute;
    bottom: -2px;
    right: -2px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
}

.user-status.active i {
    color: #48bb78;
}

.user-status.inactive i {
    color: #e53e3e;
}

.user-info {
    margin-bottom: 16px;
}

.user-name {
    font-size: 18px;
    font-weight: 600;
    color: #2d3748;
    margin: 0 0 4px 0;
}

.user-username {
    font-size: 14px;
    color: #667eea;
    margin: 0 0 4px 0;
    font-weight: 500;
}

.user-email {
    font-size: 14px;
    color: #718096;
    margin: 0 0 12px 0;
}

.user-roles {
    display: flex;
    gap: 6px;
    flex-wrap: wrap;
    margin-bottom: 12px;
}

.role-badge.more {
    background: #e2e8f0;
    color: #4a5568;
}

.user-meta {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: #718096;
}

.meta-item i {
    font-size: 12px;
}

.user-actions {
    border-top: 1px solid #e2e8f0;
    padding-top: 16px;
}

.action-buttons {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}
