// Monaco Editor Integration for LegendX IDE

let monacoLoaded = false;
let editors = new Map();

// Load Monaco Editor from CDN
async function loadMonaco() {
    if (monacoLoaded) return;
    
    try {
        // Load Monaco Editor CSS
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = 'https://cdn.jsdelivr.net/npm/monaco-editor@0.44.0/min/vs/editor/editor.main.css';
        document.head.appendChild(link);

        // Load Monaco Editor JS
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/monaco-editor@0.44.0/min/vs/loader.js';
        
        return new Promise((resolve, reject) => {
            script.onload = () => {
                require.config({ paths: { vs: 'https://cdn.jsdelivr.net/npm/monaco-editor@0.44.0/min/vs' } });
                require(['vs/editor/editor.main'], () => {
                    monacoLoaded = true;
                    resolve();
                });
            };
            script.onerror = reject;
            document.head.appendChild(script);
        });
    } catch (error) {
        console.error('Failed to load Monaco Editor:', error);
        throw error;
    }
}

// Create a new editor instance
export async function createEditor(containerId, content, language, dotNetRef) {
    try {
        await loadMonaco();
        
        const container = document.getElementById(containerId);
        if (!container) {
            throw new Error(`Container with id '${containerId}' not found`);
        }

        // Configure Monaco Editor theme
        monaco.editor.defineTheme('vs-dark-custom', {
            base: 'vs-dark',
            inherit: true,
            rules: [
                { token: 'comment', foreground: '6A9955' },
                { token: 'keyword', foreground: '569CD6' },
                { token: 'string', foreground: 'CE9178' },
                { token: 'number', foreground: 'B5CEA8' },
                { token: 'type', foreground: '4EC9B0' },
                { token: 'class', foreground: '4EC9B0' },
                { token: 'function', foreground: 'DCDCAA' },
                { token: 'variable', foreground: '9CDCFE' }
            ],
            colors: {
                'editor.background': '#1e1e1e',
                'editor.foreground': '#d4d4d4',
                'editor.lineHighlightBackground': '#2d2d30',
                'editor.selectionBackground': '#264f78',
                'editor.inactiveSelectionBackground': '#3a3d41',
                'editorCursor.foreground': '#aeafad',
                'editorWhitespace.foreground': '#404040'
            }
        });

        const editor = monaco.editor.create(container, {
            value: content || '',
            language: language || 'plaintext',
            theme: 'vs-dark-custom',
            automaticLayout: true,
            fontSize: 14,
            fontFamily: 'Consolas, Monaco, "Courier New", monospace',
            lineNumbers: 'on',
            roundedSelection: false,
            scrollBeyondLastLine: false,
            minimap: { enabled: true },
            wordWrap: 'on',
            folding: true,
            lineDecorationsWidth: 10,
            lineNumbersMinChars: 3,
            glyphMargin: true,
            contextmenu: true,
            mouseWheelZoom: true,
            smoothScrolling: true,
            cursorBlinking: 'blink',
            cursorSmoothCaretAnimation: true,
            renderWhitespace: 'selection',
            renderControlCharacters: true,
            renderLineHighlight: 'line',
            scrollbar: {
                vertical: 'visible',
                horizontal: 'visible',
                useShadows: false,
                verticalHasArrows: false,
                horizontalHasArrows: false,
                verticalScrollbarSize: 14,
                horizontalScrollbarSize: 14
            }
        });

        // Store editor reference
        editors.set(containerId, editor);

        // Set up event handlers
        let contentChangeTimeout;
        editor.onDidChangeModelContent(() => {
            clearTimeout(contentChangeTimeout);
            contentChangeTimeout = setTimeout(() => {
                const content = editor.getValue();
                dotNetRef.invokeMethodAsync('OnEditorContentChanged', content);
            }, 300); // Debounce content changes
        });

        editor.onDidChangeCursorPosition((e) => {
            dotNetRef.invokeMethodAsync('OnEditorCursorPositionChanged', e.position.lineNumber, e.position.column);
        });

        // Add keyboard shortcuts
        editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS, () => {
            // Save command - will be handled by the parent component
            console.log('Save shortcut pressed');
        });

        editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyF, () => {
            editor.getAction('actions.find').run();
        });

        editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyMod.Shift | monaco.KeyCode.KeyF, () => {
            editor.getAction('editor.action.startFindReplaceAction').run();
        });

        // Return editor wrapper object
        return {
            setValue: (value) => editor.setValue(value),
            getValue: () => editor.getValue(),
            setLanguage: (language) => monaco.editor.setModelLanguage(editor.getModel(), language),
            focus: () => editor.focus(),
            dispose: () => {
                editors.delete(containerId);
                editor.dispose();
            },
            insertText: (text) => {
                const position = editor.getPosition();
                editor.executeEdits('', [{
                    range: new monaco.Range(position.lineNumber, position.column, position.lineNumber, position.column),
                    text: text
                }]);
            },
            setTheme: (theme) => monaco.editor.setTheme(theme),
            getEditor: () => editor
        };

    } catch (error) {
        console.error('Error creating Monaco editor:', error);
        throw error;
    }
}

// Utility functions
export function getAllEditors() {
    return Array.from(editors.values());
}

export function getEditor(containerId) {
    return editors.get(containerId);
}

export function disposeEditor(containerId) {
    const editor = editors.get(containerId);
    if (editor) {
        editor.dispose();
        editors.delete(containerId);
    }
}

export function disposeAllEditors() {
    editors.forEach(editor => editor.dispose());
    editors.clear();
}

// Global IDE utilities
window.initializeIDE = function() {
    console.log('LegendX IDE initialized');
    
    // Add global keyboard shortcuts
    document.addEventListener('keydown', (e) => {
        // Prevent default browser shortcuts that might interfere
        if (e.ctrlKey || e.metaKey) {
            switch (e.key) {
                case 's':
                    e.preventDefault();
                    break;
                case 'o':
                    e.preventDefault();
                    break;
                case 'n':
                    e.preventDefault();
                    break;
            }
        }
    });
};

window.showNotification = function(message, type = 'info') {
    // Simple notification system
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #2d2d30;
        color: #d4d4d4;
        padding: 12px 16px;
        border-radius: 4px;
        border-left: 4px solid ${type === 'error' ? '#f44747' : type === 'success' ? '#4ec9b0' : '#007acc'};
        box-shadow: 0 2px 8px rgba(0,0,0,0.3);
        z-index: 10000;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        font-size: 14px;
        max-width: 300px;
        animation: slideIn 0.3s ease-out;
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease-in';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
};

window.scrollToBottom = function(element) {
    if (element) {
        element.scrollTop = element.scrollHeight;
    }
};

// Add CSS for notifications
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOut {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);
