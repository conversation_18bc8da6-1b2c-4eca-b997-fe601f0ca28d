@using LegendXManager.Shared.DTOs
@using Microsoft.JSInterop
@using LegendXManager.Shared.Services.Auth
@inject IUserManagementService UserManagementService
@inject IJSRuntime JSRuntime
@implements IDisposable

<div class="user-management">
    <div class="user-management-header">
        <div class="header-content">
            <h2 class="page-title">
                <i class="bi bi-people"></i>
                User Management
            </h2>
            <p class="page-subtitle">Manage users, roles, and permissions</p>
        </div>
        
        <div class="header-actions">
            <button class="btn btn-primary" @onclick="ShowCreateUserModal">
                <i class="bi bi-person-plus"></i>
                Add User
            </button>
            <button class="btn btn-outline-secondary" @onclick="RefreshUsers">
                <i class="bi bi-arrow-clockwise"></i>
                Refresh
            </button>
        </div>
    </div>

    <div class="user-filters">
        <div class="search-container">
            <div class="search-input-group">
                <i class="bi bi-search search-icon"></i>
                <input type="text" 
                       class="form-control search-input" 
                       placeholder="Search users by name, username, or email..."
                       @bind="searchTerm" 
                       @oninput="OnSearchChanged" />
                @if (!string.IsNullOrEmpty(searchTerm))
                {
                    <button class="btn btn-clear" @onclick="ClearSearch">
                        <i class="bi bi-x"></i>
                    </button>
                }
            </div>
        </div>
        
        <div class="filter-controls">
            <select class="form-select" value="@selectedRole" @onchange="OnRoleFilterChanged">
                <option value="">All Roles</option>
                @foreach (var role in availableRoles)
                {
                    <option value="@role">@role</option>
                }
            </select>
            
            <select class="form-select" value="@statusFilter" @onchange="OnStatusFilterChanged">
                <option value="">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="locked">Locked</option>
            </select>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="loading-container">
            <div class="loading-spinner">
                <i class="bi bi-arrow-repeat spin"></i>
                <span>Loading users...</span>
            </div>
        </div>
    }
    else if (users.Any())
    {
        <div class="users-grid">
            @foreach (var user in users)
            {
                <div class="user-card @(user.IsActive ? "" : "inactive")">
                    <div class="user-avatar">
                        @if (!string.IsNullOrEmpty(user.ProfileImageUrl))
                        {
                            <img src="@user.ProfileImageUrl" alt="@user.FullName" class="avatar-image" />
                        }
                        else
                        {
                            <div class="avatar-placeholder">
                                @GetInitials(user.FullName)
                            </div>
                        }
                        
                        <div class="user-status @(user.IsActive ? "active" : "inactive")">
                            <i class="bi @(user.IsActive ? "bi-check-circle-fill" : "bi-x-circle-fill")"></i>
                        </div>
                    </div>
                    
                    <div class="user-info">
                        <h4 class="user-name">@user.FullName</h4>
                        <p class="user-username">@@@user.Username</p>
                        <p class="user-email">@user.Email</p>
                        
                        <div class="user-roles">
                            @foreach (var role in user.Roles.Take(3))
                            {
                                <span class="role-badge">@role</span>
                            }
                            @if (user.Roles.Count > 3)
                            {
                                <span class="role-badge more">+@(user.Roles.Count - 3) more</span>
                            }
                        </div>
                        
                        <div class="user-meta">
                            <div class="meta-item">
                                <i class="bi bi-calendar"></i>
                                <span>Joined @user.CreatedAt.ToString("MMM dd, yyyy")</span>
                            </div>
                            @if (user.LastLoginAt.HasValue)
                            {
                                <div class="meta-item">
                                    <i class="bi bi-clock"></i>
                                    <span>Last login @GetRelativeTime(user.LastLoginAt.Value)</span>
                                </div>
                            }
                        </div>
                    </div>
                    
                    <div class="user-actions">
                        <div class="action-buttons">
                            <button class="btn btn-sm btn-outline-primary" 
                                    @onclick="() => EditUser(user)"
                                    title="Edit User">
                                <i class="bi bi-pencil"></i>
                            </button>
                            
                            <button class="btn btn-sm btn-outline-info" 
                                    @onclick="() => ViewUserSessions(user)"
                                    title="View Sessions">
                                <i class="bi bi-display"></i>
                            </button>
                            
                            @if (user.IsActive)
                            {
                                <button class="btn btn-sm btn-outline-warning" 
                                        @onclick="() => DeactivateUser(user)"
                                        title="Deactivate User">
                                    <i class="bi bi-pause"></i>
                                </button>
                            }
                            else
                            {
                                <button class="btn btn-sm btn-outline-success" 
                                        @onclick="() => ActivateUser(user)"
                                        title="Activate User">
                                    <i class="bi bi-play"></i>
                                </button>
                            }
                            
                            <button class="btn btn-sm btn-outline-danger" 
                                    @onclick="() => DeleteUser(user)"
                                    title="Delete User">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            }
        </div>
        
        @if (totalPages > 1)
        {
            <div class="pagination-container">
                <nav aria-label="User pagination">
                    <ul class="pagination">
                        <li class="page-item @(currentPage == 1 ? "disabled" : "")">
                            <button class="page-link" @onclick="() => ChangePage(currentPage - 1)" disabled="@(currentPage == 1)">
                                <i class="bi bi-chevron-left"></i>
                            </button>
                        </li>
                        
                        @for (int i = Math.Max(1, currentPage - 2); i <= Math.Min(totalPages, currentPage + 2); i++)
                        {
                            <li class="page-item @(i == currentPage ? "active" : "")">
                                <button class="page-link" @onclick="() => ChangePage(i)">@i</button>
                            </li>
                        }
                        
                        <li class="page-item @(currentPage == totalPages ? "disabled" : "")">
                            <button class="page-link" @onclick="() => ChangePage(currentPage + 1)" disabled="@(currentPage == totalPages)">
                                <i class="bi bi-chevron-right"></i>
                            </button>
                        </li>
                    </ul>
                </nav>
                
                <div class="pagination-info">
                    Showing @((currentPage - 1) * pageSize + 1) to @Math.Min(currentPage * pageSize, totalUsers) of @totalUsers users
                </div>
            </div>
        }
    }
    else
    {
        <div class="empty-state">
            <div class="empty-icon">
                <i class="bi bi-people"></i>
            </div>
            <h3>No users found</h3>
            <p>@(string.IsNullOrEmpty(searchTerm) ? "There are no users in the system yet." : "No users match your search criteria.")</p>
            @if (string.IsNullOrEmpty(searchTerm))
            {
                <button class="btn btn-primary" @onclick="ShowCreateUserModal">
                    <i class="bi bi-person-plus"></i>
                    Add First User
                </button>
            }
            else
            {
                <button class="btn btn-outline-secondary" @onclick="ClearSearch">
                    <i class="bi bi-x"></i>
                    Clear Search
                </button>
            }
        </div>
    }
</div>

@code {
    private List<UserDto> users = new();
    private List<string> availableRoles = new();
    private bool isLoading = true;
    private string searchTerm = "";
    private string selectedRole = "";
    private string statusFilter = "";
    private int currentPage = 1;
    private int pageSize = 12;
    private int totalUsers = 0;
    private int totalPages = 0;
    private Timer? searchTimer;

    protected override async Task OnInitializedAsync()
    {
        await LoadUsers();
        await LoadAvailableRoles();
    }

    private async Task LoadUsers()
    {
        isLoading = true;
        StateHasChanged();

        try
        {
            users = await UserManagementService.GetUsersAsync(currentPage, pageSize, searchTerm);
            // In a real implementation, you'd get total count from the API
            totalUsers = users.Count; // Placeholder
            totalPages = (int)Math.Ceiling((double)totalUsers / pageSize);
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("showNotification", $"Error loading users: {ex.Message}", "error");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task LoadAvailableRoles()
    {
        try
        {
            // In a real implementation, you'd load this from a role service
            availableRoles = new List<string> { "Admin", "User", "Manager", "Developer" };
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("showNotification", $"Error loading roles: {ex.Message}", "error");
        }
    }

    private async Task RefreshUsers()
    {
        await LoadUsers();
        await JSRuntime.InvokeVoidAsync("showNotification", "Users refreshed successfully", "success");
    }

    private void OnSearchChanged(ChangeEventArgs e)
    {
        searchTerm = e.Value?.ToString() ?? "";
        
        // Debounce search
        searchTimer?.Dispose();
        searchTimer = new Timer(async _ =>
        {
            currentPage = 1;
            await InvokeAsync(async () =>
            {
                await LoadUsers();
                StateHasChanged();
            });
        }, null, 500, Timeout.Infinite);
    }

    private Task OnRoleFilterChanged(ChangeEventArgs e)
    {
        selectedRole = e.Value?.ToString() ?? "";
        currentPage = 1;
        return LoadUsers();
    }

    private Task OnStatusFilterChanged(ChangeEventArgs e)
    {
        statusFilter = e.Value?.ToString() ?? "";
        currentPage = 1;
        return LoadUsers();
    }

    private void ClearSearch()
    {
        searchTerm = "";
        _ = OnRoleFilterChanged(new ChangeEventArgs { Value = "" });
    }

    private async Task ChangePage(int page)
    {
        if (page >= 1 && page <= totalPages)
        {
            currentPage = page;
            await LoadUsers();
        }
    }

    private void ShowCreateUserModal()
    {
        // Implement create user modal
        _ = JSRuntime.InvokeVoidAsync("showNotification", "Create user functionality would be implemented here", "info");
    }

    private void EditUser(UserDto user)
    {
        // Implement edit user functionality
        _ = JSRuntime.InvokeVoidAsync("showNotification", $"Edit user {user.Username} functionality would be implemented here", "info");
    }

    private void ViewUserSessions(UserDto user)
    {
        // Implement view user sessions functionality
        _ = JSRuntime.InvokeVoidAsync("showNotification", $"View sessions for {user.Username} functionality would be implemented here", "info");
    }

    private async Task ActivateUser(UserDto user)
    {
        try
        {
            var success = await UserManagementService.ActivateUserAsync(user.Id);
            if (success)
            {
                user.IsActive = true;
                await JSRuntime.InvokeVoidAsync("showNotification", $"User {user.Username} activated successfully", "success");
                StateHasChanged();
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("showNotification", $"Error activating user: {ex.Message}", "error");
        }
    }

    private async Task DeactivateUser(UserDto user)
    {
        try
        {
            var success = await UserManagementService.DeactivateUserAsync(user.Id);
            if (success)
            {
                user.IsActive = false;
                await JSRuntime.InvokeVoidAsync("showNotification", $"User {user.Username} deactivated successfully", "success");
                StateHasChanged();
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("showNotification", $"Error deactivating user: {ex.Message}", "error");
        }
    }

    private async Task DeleteUser(UserDto user)
    {
        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", $"Are you sure you want to delete user {user.Username}? This action cannot be undone.");
        if (confirmed)
        {
            try
            {
                var success = await UserManagementService.DeleteUserAsync(user.Id);
                if (success)
                {
                    users.Remove(user);
                    await JSRuntime.InvokeVoidAsync("showNotification", $"User {user.Username} deleted successfully", "success");
                    StateHasChanged();
                }
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("showNotification", $"Error deleting user: {ex.Message}", "error");
            }
        }
    }

    private string GetInitials(string fullName)
    {
        var parts = fullName.Split(' ', StringSplitOptions.RemoveEmptyEntries);
        if (parts.Length >= 2)
        {
            return $"{parts[0][0]}{parts[1][0]}".ToUpper();
        }
        return parts.Length > 0 ? parts[0][0].ToString().ToUpper() : "U";
    }

    private string GetRelativeTime(DateTime dateTime)
    {
        var timeSpan = DateTime.UtcNow - dateTime;
        
        if (timeSpan.TotalMinutes < 1)
            return "just now";
        if (timeSpan.TotalMinutes < 60)
            return $"{(int)timeSpan.TotalMinutes}m ago";
        if (timeSpan.TotalHours < 24)
            return $"{(int)timeSpan.TotalHours}h ago";
        if (timeSpan.TotalDays < 7)
            return $"{(int)timeSpan.TotalDays}d ago";
        
        return dateTime.ToString("MMM dd, yyyy");
    }

    public void Dispose()
    {
        searchTimer?.Dispose();
    }
}
