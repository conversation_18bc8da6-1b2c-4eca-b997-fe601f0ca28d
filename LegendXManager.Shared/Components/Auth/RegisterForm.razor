@using LegendXManager.Shared.DTOs
@using LegendXManager.Shared.Services.Auth
@using Microsoft.AspNetCore.Components.Forms
@inject IAuthService AuthService
@inject NavigationManager Navigation

<div class="register-container">
    <div class="register-card">
        <div class="register-header">
            <h2 class="register-title">
                <i class="bi bi-person-plus"></i>
                Create Account
            </h2>
            <p class="register-subtitle">Join <PERSON> and start your journey!</p>
        </div>

        <EditForm Model="registerRequest" OnValidSubmit="HandleRegister" class="register-form">
            <DataAnnotationsValidator />
            
            @if (!string.IsNullOrEmpty(errorMessage))
            {
                <div class="alert alert-danger" role="alert">
                    <i class="bi bi-exclamation-triangle"></i>
                    @errorMessage
                </div>
            }

            @if (!string.IsNullOrEmpty(successMessage))
            {
                <div class="alert alert-success" role="alert">
                    <i class="bi bi-check-circle"></i>
                    @successMessage
                </div>
            }

            <div class="form-row">
                <div class="form-group">
                    <label for="firstName" class="form-label">
                        <i class="bi bi-person"></i>
                        First Name
                    </label>
                    <InputText @bind-Value="registerRequest.FirstName" 
                              class="form-control" 
                              id="firstName" 
                              placeholder="Enter your first name"
                              disabled="@isLoading" />
                    <ValidationMessage For="@(() => registerRequest.FirstName)" class="validation-message" />
                </div>

                <div class="form-group">
                    <label for="lastName" class="form-label">
                        <i class="bi bi-person"></i>
                        Last Name
                    </label>
                    <InputText @bind-Value="registerRequest.LastName" 
                              class="form-control" 
                              id="lastName" 
                              placeholder="Enter your last name"
                              disabled="@isLoading" />
                    <ValidationMessage For="@(() => registerRequest.LastName)" class="validation-message" />
                </div>
            </div>

            <div class="form-group">
                <label for="username" class="form-label">
                    <i class="bi bi-at"></i>
                    Username
                </label>
                <InputText @bind-Value="registerRequest.Username" 
                          class="form-control" 
                          id="username" 
                          placeholder="Choose a username"
                          disabled="@isLoading" />
                <ValidationMessage For="@(() => registerRequest.Username)" class="validation-message" />
            </div>

            <div class="form-group">
                <label for="email" class="form-label">
                    <i class="bi bi-envelope"></i>
                    Email Address
                </label>
                <InputText @bind-Value="registerRequest.Email" 
                          type="email"
                          class="form-control" 
                          id="email" 
                          placeholder="Enter your email address"
                          disabled="@isLoading" />
                <ValidationMessage For="@(() => registerRequest.Email)" class="validation-message" />
            </div>

            <div class="form-group">
                <label for="phoneNumber" class="form-label">
                    <i class="bi bi-telephone"></i>
                    Phone Number (Optional)
                </label>
                <InputText @bind-Value="registerRequest.PhoneNumber" 
                          type="tel"
                          class="form-control" 
                          id="phoneNumber" 
                          placeholder="Enter your phone number"
                          disabled="@isLoading" />
            </div>

            <div class="form-group">
                <label for="password" class="form-label">
                    <i class="bi bi-lock"></i>
                    Password
                </label>
                <div class="password-input-container">
                    <InputText @bind-Value="registerRequest.Password" 
                              type="@(showPassword ? "text" : "password")"
                              class="form-control" 
                              id="password" 
                              placeholder="Create a strong password"
                              disabled="@isLoading" />
                    <button type="button" 
                            class="password-toggle-btn" 
                            @onclick="TogglePasswordVisibility"
                            disabled="@isLoading">
                        <i class="bi @(showPassword ? "bi-eye-slash" : "bi-eye")"></i>
                    </button>
                </div>
                <ValidationMessage For="@(() => registerRequest.Password)" class="validation-message" />
                <div class="password-requirements">
                    <small class="text-muted">
                        Password must contain at least 8 characters with uppercase, lowercase, number, and special character.
                    </small>
                </div>
            </div>

            <div class="form-group">
                <label for="confirmPassword" class="form-label">
                    <i class="bi bi-lock-fill"></i>
                    Confirm Password
                </label>
                <div class="password-input-container">
                    <InputText @bind-Value="registerRequest.ConfirmPassword" 
                              type="@(showConfirmPassword ? "text" : "password")"
                              class="form-control" 
                              id="confirmPassword" 
                              placeholder="Confirm your password"
                              disabled="@isLoading" />
                    <button type="button" 
                            class="password-toggle-btn" 
                            @onclick="ToggleConfirmPasswordVisibility"
                            disabled="@isLoading">
                        <i class="bi @(showConfirmPassword ? "bi-eye-slash" : "bi-eye")"></i>
                    </button>
                </div>
                <ValidationMessage For="@(() => registerRequest.ConfirmPassword)" class="validation-message" />
            </div>

            <div class="form-group">
                <div class="form-check">
                    <InputCheckbox @bind-Value="acceptTerms" 
                                  class="form-check-input" 
                                  id="acceptTerms"
                                  disabled="@isLoading" />
                    <label class="form-check-label" for="acceptTerms">
                        I agree to the <a href="/terms" target="_blank" class="link-primary">Terms of Service</a> 
                        and <a href="/privacy" target="_blank" class="link-primary">Privacy Policy</a>
                    </label>
                </div>
                @if (showTermsError)
                {
                    <div class="validation-message">
                        You must accept the terms and conditions to register.
                    </div>
                }
            </div>

            <button type="submit" 
                    class="btn btn-register" 
                    disabled="@(isLoading || !acceptTerms)">
                @if (isLoading)
                {
                    <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                    <span>Creating Account...</span>
                }
                else
                {
                    <i class="bi bi-person-plus me-2"></i>
                    <span>Create Account</span>
                }
            </button>
        </EditForm>

        <div class="register-footer">
            <p class="login-link">
                Already have an account? 
                <a href="/login" class="link-primary">Sign in here</a>
            </p>
        </div>
    </div>
</div>

<style>
    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16px;
    }
    
    .password-requirements {
        margin-top: 4px;
    }
    
    .password-requirements small {
        font-size: 12px;
        color: #718096;
    }
    
    @media (max-width: 480px) {
        .form-row {
            grid-template-columns: 1fr;
        }
    }
</style>

@code {
    [Parameter] public EventCallback<bool> OnRegistrationStateChanged { get; set; }

    private RegisterRequest registerRequest = new();
    private bool isLoading = false;
    private bool showPassword = false;
    private bool showConfirmPassword = false;
    private bool acceptTerms = false;
    private bool showTermsError = false;
    private string? errorMessage;
    private string? successMessage;

    protected override async Task OnInitializedAsync()
    {
        // Check if user is already authenticated
        if (await AuthService.IsAuthenticatedAsync())
        {
            Navigation.NavigateTo("/");
        }

        // Clear any previous messages
        errorMessage = null;
        successMessage = null;
    }

    private async Task HandleRegister()
    {
        if (isLoading) return;

        showTermsError = false;

        if (!acceptTerms)
        {
            showTermsError = true;
            return;
        }

        isLoading = true;
        errorMessage = null;
        successMessage = null;
        StateHasChanged();

        try
        {
            var response = await AuthService.RegisterAsync(registerRequest);

            if (response.Success)
            {
                successMessage = response.RequiresEmailConfirmation 
                    ? "Registration successful! Please check your email to confirm your account before signing in."
                    : "Registration successful! You can now sign in.";
                
                StateHasChanged();

                // Notify parent component
                await OnRegistrationStateChanged.InvokeAsync(true);

                // Redirect to login after a delay
                await Task.Delay(3000);
                Navigation.NavigateTo("/login");
            }
            else
            {
                errorMessage = response.Message ?? "Registration failed. Please try again.";
            }
        }
        catch (Exception ex)
        {
            errorMessage = "An unexpected error occurred. Please try again.";
            // Log the error in a real application
            Console.WriteLine($"Registration error: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private void TogglePasswordVisibility()
    {
        showPassword = !showPassword;
    }

    private void ToggleConfirmPasswordVisibility()
    {
        showConfirmPassword = !showConfirmPassword;
    }
}
