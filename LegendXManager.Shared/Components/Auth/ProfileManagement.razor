@using LegendXManager.Shared.DTOs
@using Microsoft.JSInterop
@using LegendXManager.Shared.Services.Auth
@using Microsoft.AspNetCore.Components.Forms
@inject IAuthService AuthService
@inject IJSRuntime JSRuntime

<div class="profile-management">
    <div class="profile-header">
        <div class="profile-avatar-section">
            <div class="profile-avatar">
                @if (!string.IsNullOrEmpty(currentUser?.ProfileImageUrl))
                {
                    <img src="@currentUser.ProfileImageUrl" alt="@currentUser.FullName" class="avatar-image" />
                }
                else
                {
                    <div class="avatar-placeholder">
                        @GetInitials(currentUser?.FullName ?? "")
                    </div>
                }
                
                <button class="avatar-upload-btn" @onclick="TriggerFileUpload" disabled="@isUpdating">
                    <i class="bi bi-camera"></i>
                </button>
            </div>
            
            <InputFile @ref="fileInput" 
                      OnChange="HandleFileUpload" 
                      accept="image/*" 
                      style="display: none;" />
        </div>
        
        <div class="profile-info">
            <h2 class="profile-name">@currentUser?.FullName</h2>
            <p class="profile-username">@@@currentUser?.Username</p>
            <p class="profile-email">@currentUser?.Email</p>
            
            <div class="profile-badges">
                @if (currentUser?.Roles != null)
                {
                    @foreach (var role in currentUser.Roles)
                    {
                        <span class="role-badge">@role</span>
                    }
                }
            </div>
        </div>
    </div>

    <div class="profile-tabs">
        <button class="tab-button @(activeTab == "profile" ? "active" : "")" 
                @onclick="@(() => SetActiveTab("profile"))">
            <i class="bi bi-person"></i>
            Profile Information
        </button>
        
        <button class="tab-button @(activeTab == "security" ? "active" : "")" 
                @onclick="@(() => SetActiveTab("security"))">
            <i class="bi bi-shield-lock"></i>
            Security
        </button>
        
        <button class="tab-button @(activeTab == "sessions" ? "active" : "")" 
                @onclick="@(() => SetActiveTab("sessions"))">
            <i class="bi bi-display"></i>
            Sessions
        </button>
        
        <button class="tab-button @(activeTab == "preferences" ? "active" : "")" 
                @onclick="@(() => SetActiveTab("preferences"))">
            <i class="bi bi-gear"></i>
            Preferences
        </button>
    </div>

    <div class="profile-content">
        @if (activeTab == "profile")
        {
            <div class="profile-form-section">
                <h3 class="section-title">Personal Information</h3>
                
                <EditForm Model="profileRequest" OnValidSubmit="UpdateProfile" class="profile-form">
                    <DataAnnotationsValidator />
                    
                    @if (!string.IsNullOrEmpty(successMessage))
                    {
                        <div class="alert alert-success">
                            <i class="bi bi-check-circle"></i>
                            @successMessage
                        </div>
                    }
                    
                    @if (!string.IsNullOrEmpty(errorMessage))
                    {
                        <div class="alert alert-danger">
                            <i class="bi bi-exclamation-triangle"></i>
                            @errorMessage
                        </div>
                    }

                    <div class="form-row">
                        <div class="form-group">
                            <label for="firstName" class="form-label">First Name</label>
                            <InputText @bind-Value="profileRequest.FirstName" 
                                      class="form-control" 
                                      id="firstName" 
                                      disabled="@isUpdating" />
                            <ValidationMessage For="@(() => profileRequest.FirstName)" class="validation-message" />
                        </div>

                        <div class="form-group">
                            <label for="lastName" class="form-label">Last Name</label>
                            <InputText @bind-Value="profileRequest.LastName" 
                                      class="form-control" 
                                      id="lastName" 
                                      disabled="@isUpdating" />
                            <ValidationMessage For="@(() => profileRequest.LastName)" class="validation-message" />
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="email" class="form-label">Email Address</label>
                        <InputText @bind-Value="profileRequest.Email" 
                                  type="email"
                                  class="form-control" 
                                  id="email" 
                                  disabled="@isUpdating" />
                        <ValidationMessage For="@(() => profileRequest.Email)" class="validation-message" />
                    </div>

                    <div class="form-group">
                        <label for="phoneNumber" class="form-label">Phone Number</label>
                        <InputText @bind-Value="profileRequest.PhoneNumber" 
                                  type="tel"
                                  class="form-control" 
                                  id="phoneNumber" 
                                  disabled="@isUpdating" />
                    </div>

                    <div class="form-actions">
                        <button type="submit" 
                                class="btn btn-primary" 
                                disabled="@isUpdating">
                            @if (isUpdating)
                            {
                                <span class="spinner-border spinner-border-sm me-2"></span>
                                <span>Updating...</span>
                            }
                            else
                            {
                                <i class="bi bi-check me-2"></i>
                                <span>Update Profile</span>
                            }
                        </button>
                    </div>
                </EditForm>
            </div>
        }
        else if (activeTab == "security")
        {
            <div class="security-section">
                <h3 class="section-title">Change Password</h3>
                
                <EditForm Model="passwordRequest" OnValidSubmit="ChangePassword" class="password-form">
                    <DataAnnotationsValidator />
                    
                    @if (!string.IsNullOrEmpty(passwordSuccessMessage))
                    {
                        <div class="alert alert-success">
                            <i class="bi bi-check-circle"></i>
                            @passwordSuccessMessage
                        </div>
                    }
                    
                    @if (!string.IsNullOrEmpty(passwordErrorMessage))
                    {
                        <div class="alert alert-danger">
                            <i class="bi bi-exclamation-triangle"></i>
                            @passwordErrorMessage
                        </div>
                    }

                    <div class="form-group">
                        <label for="currentPassword" class="form-label">Current Password</label>
                        <InputText @bind-Value="passwordRequest.CurrentPassword" 
                                  type="password"
                                  class="form-control" 
                                  id="currentPassword" 
                                  disabled="@isChangingPassword" />
                        <ValidationMessage For="@(() => passwordRequest.CurrentPassword)" class="validation-message" />
                    </div>

                    <div class="form-group">
                        <label for="newPassword" class="form-label">New Password</label>
                        <InputText @bind-Value="passwordRequest.NewPassword" 
                                  type="password"
                                  class="form-control" 
                                  id="newPassword" 
                                  disabled="@isChangingPassword" />
                        <ValidationMessage For="@(() => passwordRequest.NewPassword)" class="validation-message" />
                    </div>

                    <div class="form-group">
                        <label for="confirmNewPassword" class="form-label">Confirm New Password</label>
                        <InputText @bind-Value="passwordRequest.ConfirmNewPassword" 
                                  type="password"
                                  class="form-control" 
                                  id="confirmNewPassword" 
                                  disabled="@isChangingPassword" />
                        <ValidationMessage For="@(() => passwordRequest.ConfirmNewPassword)" class="validation-message" />
                    </div>

                    <div class="form-actions">
                        <button type="submit" 
                                class="btn btn-primary" 
                                disabled="@isChangingPassword">
                            @if (isChangingPassword)
                            {
                                <span class="spinner-border spinner-border-sm me-2"></span>
                                <span>Changing Password...</span>
                            }
                            else
                            {
                                <i class="bi bi-shield-check me-2"></i>
                                <span>Change Password</span>
                            }
                        </button>
                    </div>
                </EditForm>
            </div>
        }
        else if (activeTab == "sessions")
        {
            <SessionManagement />
        }
        else if (activeTab == "preferences")
        {
            <div class="preferences-section">
                <h3 class="section-title">Application Preferences</h3>
                
                <div class="preference-group">
                    <label class="preference-label">Theme</label>
                    <select class="form-select" value="@selectedTheme" @onchange="OnThemeChanged">
                        <option value="light">Light</option>
                        <option value="dark">Dark</option>
                        <option value="auto">Auto (System)</option>
                    </select>
                </div>
                
                <div class="preference-group">
                    <label class="preference-label">Language</label>
                    <select class="form-select" value="@selectedLanguage" @onchange="OnLanguageChanged">
                        <option value="en">English</option>
                        <option value="es">Spanish</option>
                        <option value="fr">French</option>
                        <option value="de">German</option>
                    </select>
                </div>
                
                <div class="preference-group">
                    <label class="preference-label">Time Zone</label>
                    <select class="form-select" value="@selectedTimeZone" @onchange="OnTimeZoneChanged">
                        <option value="UTC">UTC</option>
                        <option value="America/New_York">Eastern Time</option>
                        <option value="America/Chicago">Central Time</option>
                        <option value="America/Denver">Mountain Time</option>
                        <option value="America/Los_Angeles">Pacific Time</option>
                        <option value="Europe/London">London</option>
                        <option value="Europe/Paris">Paris</option>
                        <option value="Asia/Tokyo">Tokyo</option>
                    </select>
                </div>
            </div>
        }
    </div>
</div>

@code {
    private UserDto? currentUser;
    private UpdateProfileRequest profileRequest = new();
    private ChangePasswordRequest passwordRequest = new();
    private InputFile? fileInput;
    
    private string activeTab = "profile";
    private bool isUpdating = false;
    private bool isChangingPassword = false;
    private string? successMessage;
    private string? errorMessage;
    private string? passwordSuccessMessage;
    private string? passwordErrorMessage;
    
    private string selectedTheme = "dark";
    private string selectedLanguage = "en";
    private string selectedTimeZone = "UTC";

    protected override async Task OnInitializedAsync()
    {
        currentUser = await AuthService.GetCurrentUserAsync();
        
        if (currentUser != null)
        {
            profileRequest.FirstName = currentUser.FirstName;
            profileRequest.LastName = currentUser.LastName;
            profileRequest.Email = currentUser.Email;
            profileRequest.PhoneNumber = currentUser.PhoneNumber;
            
            selectedTheme = currentUser.Theme ?? "dark";
            selectedLanguage = currentUser.Language ?? "en";
            selectedTimeZone = currentUser.TimeZone ?? "UTC";
        }
    }

    private void SetActiveTab(string tab)
    {
        activeTab = tab;
        ClearMessages();
    }

    private void ClearMessages()
    {
        successMessage = null;
        errorMessage = null;
        passwordSuccessMessage = null;
        passwordErrorMessage = null;
    }

    private async Task UpdateProfile()
    {
        if (isUpdating) return;

        isUpdating = true;
        ClearMessages();
        StateHasChanged();

        try
        {
            var success = await AuthService.UpdateProfileAsync(profileRequest);
            if (success)
            {
                successMessage = "Profile updated successfully!";
                currentUser = await AuthService.GetCurrentUserAsync();
            }
            else
            {
                errorMessage = "Failed to update profile. Please try again.";
            }
        }
        catch (Exception ex)
        {
            errorMessage = "An error occurred while updating your profile.";
            Console.WriteLine($"Profile update error: {ex.Message}");
        }
        finally
        {
            isUpdating = false;
            StateHasChanged();
        }
    }

    private async Task ChangePassword()
    {
        if (isChangingPassword) return;

        isChangingPassword = true;
        passwordSuccessMessage = null;
        passwordErrorMessage = null;
        StateHasChanged();

        try
        {
            var success = await AuthService.ChangePasswordAsync(passwordRequest);
            if (success)
            {
                passwordSuccessMessage = "Password changed successfully!";
                passwordRequest = new ChangePasswordRequest(); // Clear form
            }
            else
            {
                passwordErrorMessage = "Failed to change password. Please check your current password.";
            }
        }
        catch (Exception ex)
        {
            passwordErrorMessage = "An error occurred while changing your password.";
            Console.WriteLine($"Password change error: {ex.Message}");
        }
        finally
        {
            isChangingPassword = false;
            StateHasChanged();
        }
    }

    private async Task TriggerFileUpload()
    {
        if (fileInput != null)
        {
            await JSRuntime.InvokeVoidAsync("triggerFileInput", fileInput.Element);
        }
    }

    private async Task HandleFileUpload(InputFileChangeEventArgs e)
    {
        var file = e.File;
        if (file != null)
        {
            try
            {
                using var stream = file.OpenReadStream(maxAllowedSize: 5 * 1024 * 1024); // 5MB limit
                var success = await AuthService.UploadProfileImageAsync(stream, file.Name);
                
                if (success)
                {
                    await JSRuntime.InvokeVoidAsync("showNotification", "Profile image updated successfully!", "success");
                    currentUser = await AuthService.GetCurrentUserAsync();
                    StateHasChanged();
                }
                else
                {
                    await JSRuntime.InvokeVoidAsync("showNotification", "Failed to upload image", "error");
                }
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("showNotification", $"Error uploading image: {ex.Message}", "error");
            }
        }
    }

    private Task OnThemeChanged(ChangeEventArgs e)
    {
        selectedTheme = e.Value?.ToString() ?? "dark";
        return JSRuntime.InvokeVoidAsync("showNotification", $"Theme changed to {selectedTheme}", "success").AsTask();
    }

    private Task OnLanguageChanged(ChangeEventArgs e)
    {
        selectedLanguage = e.Value?.ToString() ?? "en";
        return JSRuntime.InvokeVoidAsync("showNotification", $"Language changed to {selectedLanguage}", "success").AsTask();
    }

    private Task OnTimeZoneChanged(ChangeEventArgs e)
    {
        selectedTimeZone = e.Value?.ToString() ?? "UTC";
        return JSRuntime.InvokeVoidAsync("showNotification", $"Time zone changed to {selectedTimeZone}", "success").AsTask();
    }

    private string GetInitials(string fullName)
    {
        var parts = fullName.Split(' ', StringSplitOptions.RemoveEmptyEntries);
        if (parts.Length >= 2)
        {
            return $"{parts[0][0]}{parts[1][0]}".ToUpper();
        }
        return parts.Length > 0 ? parts[0][0].ToString().ToUpper() : "U";
    }
}
