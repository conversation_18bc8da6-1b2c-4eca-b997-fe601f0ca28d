@using LegendXManager.Shared.DTOs
@using LegendXManager.Shared.Services.Auth
@inject IAuthService AuthService
@inject IJSRuntime JSRuntime

<div class="session-management">
    <div class="session-header">
        <h3 class="session-title">
            <i class="bi bi-display"></i>
            Active Sessions
        </h3>
        <p class="session-subtitle">Manage your active sessions across different devices</p>
    </div>

    @if (isLoading)
    {
        <div class="loading-container">
            <div class="loading-spinner">
                <i class="bi bi-arrow-repeat spin"></i>
                <span>Loading sessions...</span>
            </div>
        </div>
    }
    else if (sessions.Any())
    {
        <div class="sessions-list">
            @foreach (var session in sessions)
            {
                <div class="session-card @(session.IsCurrent ? "current-session" : "")">
                    <div class="session-header">
                        <div class="session-device">
                            <i class="bi @GetDeviceIcon(session.DeviceType)"></i>
                            <div class="device-info">
                                <h4 class="device-name">@GetDeviceName(session)</h4>
                                <p class="device-details">@session.Platform • @session.Browser</p>
                            </div>
                        </div>
                        
                        @if (session.IsCurrent)
                        {
                            <span class="session-current">Current Session</span>
                        }
                        else
                        {
                            <button class="btn btn-revoke" 
                                    @onclick="() => RevokeSession(session)"
                                    title="Revoke Session">
                                <i class="bi bi-x-circle"></i>
                                Revoke
                            </button>
                        }
                    </div>
                    
                    <div class="session-details">
                        <div class="session-detail">
                            <span class="session-detail-label">IP Address</span>
                            <span class="session-detail-value">@(session.IpAddress ?? "Unknown")</span>
                        </div>
                        
                        <div class="session-detail">
                            <span class="session-detail-label">Location</span>
                            <span class="session-detail-value">@(session.Location ?? "Unknown")</span>
                        </div>
                        
                        <div class="session-detail">
                            <span class="session-detail-label">Created</span>
                            <span class="session-detail-value">@session.CreatedAt.ToString("MMM dd, yyyy 'at' HH:mm")</span>
                        </div>
                        
                        <div class="session-detail">
                            <span class="session-detail-label">Last Activity</span>
                            <span class="session-detail-value">
                                @if (session.LastActivityAt.HasValue)
                                {
                                    @GetRelativeTime(session.LastActivityAt.Value)
                                }
                                else
                                {
                                    <span>Never</span>
                                }
                            </span>
                        </div>
                        
                        <div class="session-detail">
                            <span class="session-detail-label">Expires</span>
                            <span class="session-detail-value @(session.IsExpired ? "expired" : "")">
                                @if (session.IsExpired)
                                {
                                    <span class="text-danger">Expired</span>
                                }
                                else
                                {
                                    @session.ExpiresAt.ToString("MMM dd, yyyy 'at' HH:mm")
                                }
                            </span>
                        </div>
                        
                        <div class="session-detail">
                            <span class="session-detail-label">Status</span>
                            <span class="session-detail-value">
                                <span class="status-badge @(session.IsActive && !session.IsExpired ? "active" : "inactive")">
                                    @(session.IsActive && !session.IsExpired ? "Active" : "Inactive")
                                </span>
                            </span>
                        </div>
                    </div>
                </div>
            }
        </div>
        
        <div class="session-actions">
            <button class="btn btn-outline-danger" @onclick="RevokeAllOtherSessions">
                <i class="bi bi-x-circle"></i>
                Revoke All Other Sessions
            </button>
            
            <button class="btn btn-outline-secondary" @onclick="RefreshSessions">
                <i class="bi bi-arrow-clockwise"></i>
                Refresh
            </button>
        </div>
    }
    else
    {
        <div class="empty-state">
            <div class="empty-icon">
                <i class="bi bi-display"></i>
            </div>
            <h3>No Active Sessions</h3>
            <p>You don't have any active sessions at the moment.</p>
        </div>
    }
</div>

<style>
    .session-management {
        max-width: 800px;
        margin: 0 auto;
        padding: 24px;
    }
    
    .session-header {
        margin-bottom: 32px;
        text-align: center;
    }
    
    .session-title {
        color: #2d3748;
        font-size: 24px;
        font-weight: 700;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 12px;
    }
    
    .session-title i {
        color: #667eea;
    }
    
    .session-subtitle {
        color: #718096;
        margin: 0;
    }
    
    .sessions-list {
        display: flex;
        flex-direction: column;
        gap: 16px;
        margin-bottom: 32px;
    }
    
    .current-session {
        border-left-color: #48bb78 !important;
        background: linear-gradient(135deg, #f0fff4 0%, #c6f6d5 100%);
    }
    
    .device-info {
        flex: 1;
    }
    
    .device-name {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #2d3748;
    }
    
    .device-details {
        margin: 0;
        font-size: 14px;
        color: #718096;
    }
    
    .status-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
    }
    
    .status-badge.active {
        background: #c6f6d5;
        color: #2f855a;
    }
    
    .status-badge.inactive {
        background: #fed7d7;
        color: #c53030;
    }
    
    .session-actions {
        display: flex;
        gap: 16px;
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .empty-state {
        text-align: center;
        padding: 60px 20px;
    }
    
    .empty-icon {
        font-size: 64px;
        color: #cbd5e0;
        margin-bottom: 24px;
    }
    
    .empty-state h3 {
        color: #2d3748;
        margin-bottom: 12px;
    }
    
    .empty-state p {
        color: #718096;
        margin: 0;
    }
    
    .text-danger {
        color: #e53e3e !important;
    }
    
    .expired {
        color: #e53e3e;
    }
</style>

@code {
    private List<SessionDto> sessions = new();
    private bool isLoading = true;

    protected override async Task OnInitializedAsync()
    {
        await LoadSessions();
    }

    private async Task LoadSessions()
    {
        isLoading = true;
        StateHasChanged();

        try
        {
            sessions = await AuthService.GetActiveSessionsAsync();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("showNotification", $"Error loading sessions: {ex.Message}", "error");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task RefreshSessions()
    {
        await LoadSessions();
        await JSRuntime.InvokeVoidAsync("showNotification", "Sessions refreshed", "success");
    }

    private async Task RevokeSession(SessionDto session)
    {
        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", 
            $"Are you sure you want to revoke this session from {session.DeviceType}?");
        
        if (confirmed)
        {
            try
            {
                var success = await AuthService.RevokeSessionAsync(session.Id);
                if (success)
                {
                    sessions.Remove(session);
                    await JSRuntime.InvokeVoidAsync("showNotification", "Session revoked successfully", "success");
                    StateHasChanged();
                }
                else
                {
                    await JSRuntime.InvokeVoidAsync("showNotification", "Failed to revoke session", "error");
                }
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("showNotification", $"Error revoking session: {ex.Message}", "error");
            }
        }
    }

    private async Task RevokeAllOtherSessions()
    {
        var otherSessionsCount = sessions.Count(s => !s.IsCurrent);
        if (otherSessionsCount == 0)
        {
            await JSRuntime.InvokeVoidAsync("showNotification", "No other sessions to revoke", "info");
            return;
        }

        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", 
            $"Are you sure you want to revoke all {otherSessionsCount} other sessions? This will log you out from all other devices.");
        
        if (confirmed)
        {
            try
            {
                var success = await AuthService.RevokeAllOtherSessionsAsync();
                if (success)
                {
                    sessions = sessions.Where(s => s.IsCurrent).ToList();
                    await JSRuntime.InvokeVoidAsync("showNotification", "All other sessions revoked successfully", "success");
                    StateHasChanged();
                }
                else
                {
                    await JSRuntime.InvokeVoidAsync("showNotification", "Failed to revoke sessions", "error");
                }
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("showNotification", $"Error revoking sessions: {ex.Message}", "error");
            }
        }
    }

    private string GetDeviceIcon(string? deviceType)
    {
        return deviceType?.ToLower() switch
        {
            "mobile" => "bi-phone",
            "tablet" => "bi-tablet",
            "desktop" => "bi-display",
            "laptop" => "bi-laptop",
            _ => "bi-device-hdd"
        };
    }

    private string GetDeviceName(SessionDto session)
    {
        if (!string.IsNullOrEmpty(session.DeviceType))
        {
            return $"{session.DeviceType} Device";
        }
        
        if (!string.IsNullOrEmpty(session.Platform))
        {
            return session.Platform;
        }
        
        return "Unknown Device";
    }

    private string GetRelativeTime(DateTime dateTime)
    {
        var timeSpan = DateTime.UtcNow - dateTime;
        
        if (timeSpan.TotalMinutes < 1)
            return "just now";
        if (timeSpan.TotalMinutes < 60)
            return $"{(int)timeSpan.TotalMinutes} minutes ago";
        if (timeSpan.TotalHours < 24)
            return $"{(int)timeSpan.TotalHours} hours ago";
        if (timeSpan.TotalDays < 7)
            return $"{(int)timeSpan.TotalDays} days ago";
        
        return dateTime.ToString("MMM dd, yyyy");
    }
}
