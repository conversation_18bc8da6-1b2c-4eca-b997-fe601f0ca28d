@using LegendXManager.Shared.DTOs
@using Microsoft.JSInterop
@using LegendXManager.Shared.Services.Auth
@using Microsoft.AspNetCore.Components.Forms
@inject IAuthService AuthService
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime

<div class="login-container">
    <div class="login-card">
        <div class="login-header">
            <h2 class="login-title">
                <i class="bi bi-shield-lock"></i>
                Sign In to LegendX
            </h2>
            <p class="login-subtitle">Welcome back! Please sign in to your account.</p>
        </div>

        <EditForm Model="loginRequest" OnValidSubmit="HandleLogin" class="login-form">
            <DataAnnotationsValidator />
            
            @if (!string.IsNullOrEmpty(errorMessage))
            {
                <div class="alert alert-danger" role="alert">
                    <i class="bi bi-exclamation-triangle"></i>
                    @errorMessage
                </div>
            }

            @if (!string.IsNullOrEmpty(successMessage))
            {
                <div class="alert alert-success" role="alert">
                    <i class="bi bi-check-circle"></i>
                    @successMessage
                </div>
            }

            <div class="form-group">
                <label for="username" class="form-label">
                    <i class="bi bi-person"></i>
                    Username or Email
                </label>
                <InputText @bind-Value="loginRequest.Username" 
                          class="form-control" 
                          id="username" 
                          placeholder="Enter your username or email"
                          disabled="@isLoading" />
                <ValidationMessage For="@(() => loginRequest.Username)" class="validation-message" />
            </div>

            <div class="form-group">
                <label for="password" class="form-label">
                    <i class="bi bi-lock"></i>
                    Password
                </label>
                <div class="password-input-container">
                    <InputText @bind-Value="loginRequest.Password" 
                              type="@(showPassword ? "text" : "password")"
                              class="form-control" 
                              id="password" 
                              placeholder="Enter your password"
                              disabled="@isLoading" />
                    <button type="button" 
                            class="password-toggle-btn" 
                            @onclick="TogglePasswordVisibility"
                            disabled="@isLoading">
                        <i class="bi @(showPassword ? "bi-eye-slash" : "bi-eye")"></i>
                    </button>
                </div>
                <ValidationMessage For="@(() => loginRequest.Password)" class="validation-message" />
            </div>

            <div class="form-options">
                <div class="form-check">
                    <InputCheckbox @bind-Value="loginRequest.RememberMe" 
                                  class="form-check-input" 
                                  id="rememberMe"
                                  disabled="@isLoading" />
                    <label class="form-check-label" for="rememberMe">
                        Remember me
                    </label>
                </div>
                
                <a href="/forgot-password" class="forgot-password-link">
                    Forgot password?
                </a>
            </div>

            <button type="submit" 
                    class="btn btn-primary btn-login" 
                    disabled="@isLoading">
                @if (isLoading)
                {
                    <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                    <span>Signing in...</span>
                }
                else
                {
                    <i class="bi bi-box-arrow-in-right me-2"></i>
                    <span>Sign In</span>
                }
            </button>
        </EditForm>

        <div class="login-footer">
            <p class="register-link">
                Don't have an account? 
                <a href="/register" class="link-primary">Sign up here</a>
            </p>
        </div>
    </div>
</div>

@code {
    [Parameter] public string? ReturnUrl { get; set; }
    [Parameter] public EventCallback<bool> OnLoginStateChanged { get; set; }

    private LoginRequest loginRequest = new();
    private bool isLoading = false;
    private bool showPassword = false;
    private string? errorMessage;
    private string? successMessage;

    protected override async Task OnInitializedAsync()
    {
        // Check if user is already authenticated
        if (await AuthService.IsAuthenticatedAsync())
        {
            var returnUrl = ReturnUrl ?? "/";
            Navigation.NavigateTo(returnUrl);
        }

        // Clear any previous messages
        errorMessage = null;
        successMessage = null;
    }

    private async Task HandleLogin()
    {
        if (isLoading) return;

        isLoading = true;
        errorMessage = null;
        successMessage = null;
        StateHasChanged();

        try
        {
            var response = await AuthService.LoginAsync(loginRequest);

            if (response.Success)
            {
                successMessage = "Login successful! Redirecting...";
                StateHasChanged();

                // Notify parent component
                await OnLoginStateChanged.InvokeAsync(true);

                // Small delay to show success message
                await Task.Delay(1000);

                // Redirect to return URL or dashboard
                var returnUrl = ReturnUrl ?? "/";
                Navigation.NavigateTo(returnUrl);
            }
            else
            {
                if (response.RequiresEmailConfirmation)
                {
                    errorMessage = "Please confirm your email address before signing in. Check your inbox for the confirmation link.";
                }
                else if (response.RequiresTwoFactor)
                {
                    // Handle two-factor authentication
                    Navigation.NavigateTo($"/two-factor?returnUrl={Uri.EscapeDataString(ReturnUrl ?? "/")}");
                }
                else
                {
                    errorMessage = response.Message ?? "Login failed. Please check your credentials and try again.";
                }
            }
        }
        catch (Exception ex)
        {
            errorMessage = "An unexpected error occurred. Please try again.";
            // Log the error in a real application
            Console.WriteLine($"Login error: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private void TogglePasswordVisibility()
    {
        showPassword = !showPassword;
    }

    private void HandleForgotPassword()
    {
        Navigation.NavigateTo("/forgot-password");
    }

    private void HandleRegister()
    {
        Navigation.NavigateTo("/register");
    }
}
