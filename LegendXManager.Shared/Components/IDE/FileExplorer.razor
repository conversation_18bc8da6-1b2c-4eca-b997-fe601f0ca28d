@using Microsoft.JSInterop
@inject IJSRuntime JSRuntime

<div class="file-explorer">
    <div class="file-explorer-header">
        <h6 class="mb-0">Explorer</h6>
        <div class="file-explorer-actions">
            <button class="btn btn-sm btn-outline-light" @onclick="RefreshFiles" title="Refresh">
                <i class="bi bi-arrow-clockwise"></i>
            </button>
            <button class="btn btn-sm btn-outline-light" @onclick="NewFolder" title="New Folder">
                <i class="bi bi-folder-plus"></i>
            </button>
            <button class="btn btn-sm btn-outline-light" @onclick="NewFile" title="New File">
                <i class="bi bi-file-plus"></i>
            </button>
        </div>
    </div>

    <div class="file-tree">
        @if (rootFolder != null)
        {
            <FileTreeNode Node="rootFolder" 
                         OnFileSelected="OnFileSelected" 
                         OnFolderToggled="OnFolderToggled" 
                         Level="0" />
        }
        else
        {
            <div class="loading-files">
                <i class="bi bi-arrow-repeat spin"></i> Loading files...
            </div>
        }
    </div>
</div>

@code {
    [Parameter] public EventCallback<(string filePath, string fileName)> OnFileSelected { get; set; }

    private FileNode? rootFolder;

    public class FileNode
    {
        public string Name { get; set; } = "";
        public string Path { get; set; } = "";
        public bool IsFolder { get; set; }
        public bool IsExpanded { get; set; }
        public List<FileNode> Children { get; set; } = new();
        public FileNode? Parent { get; set; }
    }

    protected override async Task OnInitializedAsync()
    {
        await LoadFileTree();
    }

    private async Task LoadFileTree()
    {
        // Simulate loading file tree
        await Task.Delay(500);
        
        rootFolder = new FileNode
        {
            Name = "Project",
            Path = "/",
            IsFolder = true,
            IsExpanded = true,
            Children = new List<FileNode>
            {
                new FileNode
                {
                    Name = "src",
                    Path = "/src",
                    IsFolder = true,
                    IsExpanded = false,
                    Children = new List<FileNode>
                    {
                        new FileNode { Name = "Program.cs", Path = "/src/Program.cs", IsFolder = false },
                        new FileNode { Name = "Models", Path = "/src/Models", IsFolder = true, IsExpanded = false,
                            Children = new List<FileNode>
                            {
                                new FileNode { Name = "User.cs", Path = "/src/Models/User.cs", IsFolder = false },
                                new FileNode { Name = "Product.cs", Path = "/src/Models/Product.cs", IsFolder = false }
                            }
                        },
                        new FileNode { Name = "Services", Path = "/src/Services", IsFolder = true, IsExpanded = false,
                            Children = new List<FileNode>
                            {
                                new FileNode { Name = "UserService.cs", Path = "/src/Services/UserService.cs", IsFolder = false },
                                new FileNode { Name = "ProductService.cs", Path = "/src/Services/ProductService.cs", IsFolder = false }
                            }
                        }
                    }
                },
                new FileNode
                {
                    Name = "wwwroot",
                    Path = "/wwwroot",
                    IsFolder = true,
                    IsExpanded = false,
                    Children = new List<FileNode>
                    {
                        new FileNode { Name = "index.html", Path = "/wwwroot/index.html", IsFolder = false },
                        new FileNode { Name = "app.css", Path = "/wwwroot/app.css", IsFolder = false },
                        new FileNode { Name = "app.js", Path = "/wwwroot/app.js", IsFolder = false }
                    }
                },
                new FileNode { Name = "README.md", Path = "/README.md", IsFolder = false },
                new FileNode { Name = "appsettings.json", Path = "/appsettings.json", IsFolder = false },
                new FileNode { Name = ".gitignore", Path = "/.gitignore", IsFolder = false }
            }
        };

        StateHasChanged();
    }

    private async Task RefreshFiles()
    {
        rootFolder = null;
        StateHasChanged();
        await LoadFileTree();
    }

    private async Task NewFolder()
    {
        // Implement new folder creation
        await JSRuntime.InvokeVoidAsync("showNotification", "New folder functionality would be implemented here");
    }

    private async Task NewFile()
    {
        // Implement new file creation
        await JSRuntime.InvokeVoidAsync("showNotification", "New file functionality would be implemented here");
    }

    private async Task OnFolderToggled(FileNode folder)
    {
        folder.IsExpanded = !folder.IsExpanded;
        StateHasChanged();
    }

    private async Task HandleFileSelected(string filePath, string fileName)
    {
        await OnFileSelected.InvokeAsync((filePath, fileName));
    }
}

<div class="file-tree-node" style="margin-left: @(Level * 16)px">
    @if (Node.IsFolder)
    {
        <div class="folder-item" @onclick="() => OnFolderToggled.InvokeAsync(Node)">
            <i class="bi @(Node.IsExpanded ? "bi-folder2-open" : "bi-folder2") folder-icon"></i>
            <span class="node-name">@Node.Name</span>
        </div>
        
        @if (Node.IsExpanded)
        {
            @foreach (var child in Node.Children)
            {
                <FileTreeNode Node="child" 
                             OnFileSelected="OnFileSelected" 
                             OnFolderToggled="OnFolderToggled" 
                             Level="Level + 1" />
            }
        }
    }
    else
    {
        <div class="file-item" @onclick="() => OnFileSelected.InvokeAsync((Node.Path, Node.Name))">
            <i class="bi @GetFileIcon(Node.Name) file-icon"></i>
            <span class="node-name">@Node.Name</span>
        </div>
    }
</div>

@code {
    [Parameter] public FileNode Node { get; set; } = new();
    [Parameter] public EventCallback<(string filePath, string fileName)> OnFileSelected { get; set; }
    [Parameter] public EventCallback<FileNode> OnFolderToggled { get; set; }
    [Parameter] public int Level { get; set; }

    private string GetFileIcon(string fileName)
    {
        var extension = Path.GetExtension(fileName).ToLower();
        return extension switch
        {
            ".cs" => "bi-file-earmark-code",
            ".js" => "bi-file-earmark-code",
            ".ts" => "bi-file-earmark-code",
            ".html" => "bi-file-earmark-code",
            ".css" => "bi-file-earmark-code",
            ".json" => "bi-file-earmark-text",
            ".xml" => "bi-file-earmark-text",
            ".razor" => "bi-file-earmark-code",
            ".py" => "bi-file-earmark-code",
            ".java" => "bi-file-earmark-code",
            ".cpp" or ".c" => "bi-file-earmark-code",
            ".md" => "bi-file-earmark-text",
            ".txt" => "bi-file-earmark-text",
            ".pdf" => "bi-file-earmark-pdf",
            ".png" or ".jpg" or ".jpeg" or ".gif" or ".svg" => "bi-file-earmark-image",
            _ => "bi-file-earmark"
        };
    }
}

@* This is a separate component that should be in its own file *@
@* FileTreeNode.razor *@
