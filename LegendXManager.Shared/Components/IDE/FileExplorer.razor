@using Microsoft.JSInterop
@implements IDisposable
@inject IJSRuntime JSRuntime

<div class="file-explorer">
    <div class="file-explorer-header">
        <h6 class="mb-0">Explorer</h6>
        <div class="file-explorer-actions">
            <button class="btn btn-sm btn-outline-light" @onclick="RefreshFiles" title="Refresh">
                <i class="bi bi-arrow-clockwise"></i>
            </button>
            <button class="btn btn-sm btn-outline-light" @onclick="NewFolder" title="New Folder">
                <i class="bi bi-folder-plus"></i>
            </button>
            <button class="btn btn-sm btn-outline-light" @onclick="NewFile" title="New File">
                <i class="bi bi-file-plus"></i>
            </button>
        </div>
    </div>

    <div class="file-tree">
        @if (rootFolder != null)
        {
            @RenderNode(rootFolder, 0)
        }
        else
        {
            <div class="loading-files">
                <i class="bi bi-arrow-repeat spin"></i> Loading files...
            </div>
        }
    </div>
</div>

@code {
    [Parameter] public EventCallback<(string filePath, string fileName)> OnFileSelected { get; set; }

    private FileNode? rootFolder;

    public class FileNode
    {
        public string Name { get; set; } = "";
        public string Path { get; set; } = "";
        public bool IsFolder { get; set; }
        public bool IsExpanded { get; set; }
        public List<FileNode> Children { get; set; } = new();
        public FileNode? Parent { get; set; }
    }

    private RenderFragment RenderNode(FileNode node, int level) => builder =>
    {
        builder.OpenElement(0, "div");
        builder.AddAttribute(1, "class", "file-tree-node");
        builder.AddAttribute(2, "style", $"margin-left: {level * 16}px");

        if (node.IsFolder)
        {
            builder.OpenElement(3, "div");
            builder.AddAttribute(4, "class", "folder-item");
            builder.AddAttribute(5, "onclick", EventCallback.Factory.Create(this, () => ToggleFolder(node)));

            builder.OpenElement(6, "i");
            builder.AddAttribute(7, "class", $"bi {(node.IsExpanded ? "bi-folder2-open" : "bi-folder2")} folder-icon");
            builder.CloseElement();

            builder.OpenElement(8, "span");
            builder.AddAttribute(9, "class", "node-name");
            builder.AddContent(10, node.Name);
            builder.CloseElement();

            builder.CloseElement(); // folder-item

            if (node.IsExpanded)
            {
                foreach (var child in node.Children)
                {
                    builder.AddContent(11, RenderNode(child, level + 1));
                }
            }
        }
        else
        {
            builder.OpenElement(12, "div");
            builder.AddAttribute(13, "class", "file-item");
            builder.AddAttribute(14, "onclick", EventCallback.Factory.Create(this, () => SelectFile(node.Path, node.Name)));

            builder.OpenElement(15, "i");
            builder.AddAttribute(16, "class", $"bi {GetFileIcon(node.Name)} file-icon");
            builder.CloseElement();

            builder.OpenElement(17, "span");
            builder.AddAttribute(18, "class", "node-name");
            builder.AddContent(19, node.Name);
            builder.CloseElement();

            builder.CloseElement(); // file-item
        }

        builder.CloseElement(); // file-tree-node
    };

    protected override async Task OnInitializedAsync()
    {
        await LoadFileTree();
    }

    private async Task LoadFileTree()
    {
        await Task.Delay(500);
        
        rootFolder = new FileNode
        {
            Name = "Project",
            Path = "/",
            IsFolder = true,
            IsExpanded = true,
            Children = new List<FileNode>
            {
                new FileNode
                {
                    Name = "src",
                    Path = "/src",
                    IsFolder = true,
                    IsExpanded = false,
                    Children = new List<FileNode>
                    {
                        new FileNode { Name = "Program.cs", Path = "/src/Program.cs", IsFolder = false },
                        new FileNode { Name = "Models", Path = "/src/Models", IsFolder = true, IsExpanded = false,
                            Children = new List<FileNode>
                            {
                                new FileNode { Name = "User.cs", Path = "/src/Models/User.cs", IsFolder = false },
                                new FileNode { Name = "Product.cs", Path = "/src/Models/Product.cs", IsFolder = false }
                            }
                        }
                    }
                },
                new FileNode { Name = "README.md", Path = "/README.md", IsFolder = false },
                new FileNode { Name = "appsettings.json", Path = "/appsettings.json", IsFolder = false }
            }
        };

        StateHasChanged();
    }

    private async Task RefreshFiles()
    {
        rootFolder = null;
        StateHasChanged();
        await LoadFileTree();
    }

    private async Task NewFolder()
    {
        await JSRuntime.InvokeVoidAsync("showNotification", "New folder functionality would be implemented here");
    }

    private async Task NewFile()
    {
        await JSRuntime.InvokeVoidAsync("showNotification", "New file functionality would be implemented here");
    }

    private void ToggleFolder(FileNode folder)
    {
        folder.IsExpanded = !folder.IsExpanded;
        StateHasChanged();
    }

    private async Task SelectFile(string filePath, string fileName)
    {
        await OnFileSelected.InvokeAsync((filePath, fileName));
    }

    private string GetFileIcon(string fileName)
    {
        var extension = Path.GetExtension(fileName).ToLower();
        return extension switch
        {
            ".cs" => "bi-file-earmark-code",
            ".js" => "bi-file-earmark-code",
            ".ts" => "bi-file-earmark-code",
            ".html" => "bi-file-earmark-code",
            ".css" => "bi-file-earmark-code",
            ".json" => "bi-file-earmark-text",
            ".xml" => "bi-file-earmark-text",
            ".razor" => "bi-file-earmark-code",
            ".py" => "bi-file-earmark-code",
            ".java" => "bi-file-earmark-code",
            ".cpp" or ".c" => "bi-file-earmark-code",
            ".md" => "bi-file-earmark-text",
            ".txt" => "bi-file-earmark-text",
            ".pdf" => "bi-file-earmark-pdf",
            ".png" or ".jpg" or ".jpeg" or ".gif" or ".svg" => "bi-file-earmark-image",
            _ => "bi-file-earmark"
        };
    }
}
    public void Dispose() { /* No resources to dispose */ }
