@using LegendXManager.Shared.Components.IDE

<div class="file-tree-node" style="margin-left: @(Level * 16)px">
    @if (Node.IsFolder)
    {
        <div class="folder-item" @onclick="() => OnFolderToggled.InvokeAsync(Node)">
            <i class="bi @(Node.IsExpanded ? "bi-folder2-open" : "bi-folder2") folder-icon"></i>
            <span class="node-name">@Node.Name</span>
        </div>
        
        @if (Node.IsExpanded)
        {
            @foreach (var child in Node.Children)
            {
                <FileTreeNode Node="child" 
                             OnFileSelected="OnFileSelected" 
                             OnFolderToggled="OnFolderToggled" 
                             Level="Level + 1" />
            }
        }
    }
    else
    {
        <div class="file-item" @onclick="() => OnFileSelected.InvokeAsync((Node.Path, Node.Name))">
            <i class="bi @GetFileIcon(Node.Name) file-icon"></i>
            <span class="node-name">@Node.Name</span>
        </div>
    }
</div>

@code {
    [Parameter] public FileExplorer.FileNode Node { get; set; } = new();
    [Parameter] public EventCallback<(string filePath, string fileName)> OnFileSelected { get; set; }
    [Parameter] public EventCallback<FileExplorer.FileNode> OnFolderToggled { get; set; }
    [Parameter] public int Level { get; set; }

    private string GetFileIcon(string fileName)
    {
        var extension = Path.GetExtension(fileName).ToLower();
        return extension switch
        {
            ".cs" => "bi-file-earmark-code",
            ".js" => "bi-file-earmark-code",
            ".ts" => "bi-file-earmark-code",
            ".html" => "bi-file-earmark-code",
            ".css" => "bi-file-earmark-code",
            ".json" => "bi-file-earmark-text",
            ".xml" => "bi-file-earmark-text",
            ".razor" => "bi-file-earmark-code",
            ".py" => "bi-file-earmark-code",
            ".java" => "bi-file-earmark-code",
            ".cpp" or ".c" => "bi-file-earmark-code",
            ".md" => "bi-file-earmark-text",
            ".txt" => "bi-file-earmark-text",
            ".pdf" => "bi-file-earmark-pdf",
            ".png" or ".jpg" or ".jpeg" or ".gif" or ".svg" => "bi-file-earmark-image",
            _ => "bi-file-earmark"
        };
    }
}
