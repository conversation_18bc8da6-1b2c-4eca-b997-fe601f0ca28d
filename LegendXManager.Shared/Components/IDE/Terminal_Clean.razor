@using Microsoft.JSInterop
@inject IJSRuntime JSRuntime
@implements IDisposable

<div class="terminal">
    <div class="terminal-output" @ref="terminalOutput">
        @foreach (var line in outputLines)
        {
            <div class="terminal-line @line.Type">
                <span class="terminal-prompt">$</span>
                <span class="terminal-text">@line.Text</span>
            </div>
        }
    </div>
    
    <div class="terminal-input-container">
        <span class="terminal-prompt">$</span>
        <input @ref="terminalInput" 
               @bind="currentInput" 
               @onkeypress="HandleKeyPress"
               class="terminal-input" 
               placeholder="Type a command..." 
               autocomplete="off" />
    </div>
</div>

@code {
    private ElementReference terminalOutput;
    private ElementReference terminalInput;
    private string currentInput = "";
    private List<TerminalLine> outputLines = new();

    public class TerminalLine
    {
        public string Text { get; set; } = "";
        public string Type { get; set; } = ""; // command, error, success, info
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await AddWelcomeMessage();
            await terminalInput.FocusAsync();
        }
    }

    private async Task AddWelcomeMessage()
    {
        outputLines.Add(new TerminalLine { Text = "Welcome to LegendX Terminal", Type = "info" });
        outputLines.Add(new TerminalLine { Text = "Type 'help' for available commands", Type = "info" });
        StateHasChanged();
        await ScrollToBottom();
    }

    private async Task HandleKeyPress(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            await ExecuteCommand(currentInput);
            currentInput = "";
        }
    }

    private async Task ExecuteCommand(string command)
    {
        if (string.IsNullOrWhiteSpace(command))
            return;

        // Add command to output
        outputLines.Add(new TerminalLine { Text = command, Type = "command" });

        // Process command
        var response = await ProcessCommand(command.Trim());
        outputLines.Add(new TerminalLine { Text = response.Text, Type = response.Type });

        StateHasChanged();
        await ScrollToBottom();
    }

    private async Task<TerminalLine> ProcessCommand(string command)
    {
        await Task.Delay(100); // Simulate processing time

        var parts = command.Split(' ', StringSplitOptions.RemoveEmptyEntries);
        var cmd = parts[0].ToLower();

        return cmd switch
        {
            "help" => new TerminalLine { Text = GetHelpText(), Type = "info" },
            "clear" => await ClearTerminal(),
            "echo" => new TerminalLine { Text = string.Join(" ", parts.Skip(1)), Type = "info" },
            "date" => new TerminalLine { Text = DateTime.Now.ToString("F"), Type = "info" },
            "pwd" => new TerminalLine { Text = "/workspace/LegendXManager", Type = "info" },
            "ls" or "dir" => new TerminalLine { Text = "src/  wwwroot/  README.md  appsettings.json", Type = "info" },
            "dotnet" => await ProcessDotNetCommand(parts.Skip(1).ToArray()),
            "npm" => await ProcessNpmCommand(parts.Skip(1).ToArray()),
            "git" => await ProcessGitCommand(parts.Skip(1).ToArray()),
            _ => new TerminalLine { Text = $"Command '{cmd}' not found. Type 'help' for available commands.", Type = "error" }
        };
    }

    private async Task<TerminalLine> ClearTerminal()
    {
        outputLines.Clear();
        StateHasChanged();
        return new TerminalLine { Text = "", Type = "info" };
    }

    private async Task<TerminalLine> ProcessDotNetCommand(string[] args)
    {
        if (args.Length == 0)
            return new TerminalLine { Text = ".NET SDK version 8.0.0", Type = "success" };

        var subCommand = args[0].ToLower();
        return subCommand switch
        {
            "build" => new TerminalLine { Text = "Build succeeded. 0 Warning(s) 0 Error(s)", Type = "success" },
            "run" => new TerminalLine { Text = "Application started. Press Ctrl+C to shut down.", Type = "success" },
            "test" => new TerminalLine { Text = "Test run completed. Passed: 15, Failed: 0, Skipped: 0", Type = "success" },
            "restore" => new TerminalLine { Text = "Restore completed successfully.", Type = "success" },
            "clean" => new TerminalLine { Text = "Clean completed successfully.", Type = "success" },
            _ => new TerminalLine { Text = $"Unknown dotnet command: {subCommand}", Type = "error" }
        };
    }

    private async Task<TerminalLine> ProcessNpmCommand(string[] args)
    {
        if (args.Length == 0)
            return new TerminalLine { Text = "npm version 10.2.0", Type = "success" };

        var subCommand = args[0].ToLower();
        return subCommand switch
        {
            "install" or "i" => new TerminalLine { Text = "Dependencies installed successfully.", Type = "success" },
            "start" => new TerminalLine { Text = "Development server started on http://localhost:3000", Type = "success" },
            "build" => new TerminalLine { Text = "Build completed successfully.", Type = "success" },
            "test" => new TerminalLine { Text = "All tests passed.", Type = "success" },
            _ => new TerminalLine { Text = $"Unknown npm command: {subCommand}", Type = "error" }
        };
    }

    private async Task<TerminalLine> ProcessGitCommand(string[] args)
    {
        if (args.Length == 0)
            return new TerminalLine { Text = "git version 2.42.0", Type = "success" };

        var subCommand = args[0].ToLower();
        return subCommand switch
        {
            "status" => new TerminalLine { Text = "On branch main\nYour branch is up to date with 'origin/main'.\nnothing to commit, working tree clean", Type = "success" },
            "pull" => new TerminalLine { Text = "Already up to date.", Type = "success" },
            "push" => new TerminalLine { Text = "Everything up-to-date", Type = "success" },
            "log" => new TerminalLine { Text = "commit abc123 (HEAD -> main)\nAuthor: Developer\nDate: " + DateTime.Now.ToString("ddd MMM dd HH:mm:ss yyyy"), Type = "info" },
            _ => new TerminalLine { Text = $"Unknown git command: {subCommand}", Type = "error" }
        };
    }

    private string GetHelpText()
    {
        return @"Available commands:
  help          - Show this help message
  clear         - Clear the terminal
  echo <text>   - Echo text back
  date          - Show current date and time
  pwd           - Show current directory
  ls/dir        - List directory contents
  dotnet <cmd>  - .NET CLI commands (build, run, test, restore, clean)
  npm <cmd>     - NPM commands (install, start, build, test)
  git <cmd>     - Git commands (status, pull, push, log)";
    }

    private async Task ScrollToBottom()
    {
        await JSRuntime.InvokeVoidAsync("scrollToBottom", terminalOutput);
    }

    public async Task WriteOutput(string text, string type = "info")
    {
        outputLines.Add(new TerminalLine { Text = text, Type = type });
        StateHasChanged();
        await ScrollToBottom();
    }

    public void Dispose()
    {
        // No resources to dispose
    }
}
