@using Microsoft.JSInterop
@inject IJSRuntime JSRuntime

<div class="status-bar">
    <div class="status-left">
        <div class="status-item">
            <i class="bi bi-git"></i>
            <span>main</span>
        </div>
        
        <div class="status-item">
            <i class="bi bi-exclamation-triangle"></i>
            <span>@errorCount</span>
        </div>
        
        <div class="status-item">
            <i class="bi bi-exclamation-circle"></i>
            <span>@warningCount</span>
        </div>
    </div>

    <div class="status-center">
        @if (!string.IsNullOrEmpty(CurrentFile))
        {
            <div class="status-item">
                <i class="bi bi-file-earmark"></i>
                <span>@CurrentFile</span>
            </div>
        }
    </div>

    <div class="status-right">
        @if (!string.IsNullOrEmpty(Language))
        {
            <div class="status-item clickable" @onclick="ShowLanguageSelector">
                <span>@GetLanguageDisplayName(Language)</span>
            </div>
        }
        
        <div class="status-item">
            <span>UTF-8</span>
        </div>
        
        <div class="status-item">
            <span>LF</span>
        </div>
        
        @if (LineNumber > 0)
        {
            <div class="status-item clickable" @onclick="ShowGoToLine">
                <span>Ln @LineNumber, Col @ColumnNumber</span>
            </div>
        }
        
        <div class="status-item">
            <i class="bi bi-bell"></i>
        </div>
    </div>
</div>

@if (showLanguageSelector)
{
    <div class="language-selector-overlay" @onclick="HideLanguageSelector">
        <div class="language-selector" @onclick:stopPropagation="true">
            <div class="language-selector-header">
                <h6>Select Language Mode</h6>
                <button class="btn btn-sm btn-outline-secondary" @onclick="HideLanguageSelector">
                    <i class="bi bi-x"></i>
                </button>
            </div>
            <div class="language-list">
                @foreach (var lang in availableLanguages)
                {
                    <div class="language-item @(lang.Key == Language ? "active" : "")" 
                         @onclick="() => SelectLanguage(lang.Key)">
                        <span>@lang.Value</span>
                    </div>
                }
            </div>
        </div>
    </div>
}

@code {
    [Parameter] public string? CurrentFile { get; set; }
    [Parameter] public int LineNumber { get; set; } = 1;
    [Parameter] public int ColumnNumber { get; set; } = 1;
    [Parameter] public string? Language { get; set; }
    [Parameter] public EventCallback<string> OnLanguageChanged { get; set; }

    private int errorCount = 0;
    private int warningCount = 2;
    private bool showLanguageSelector = false;

    private readonly Dictionary<string, string> availableLanguages = new()
    {
        { "plaintext", "Plain Text" },
        { "csharp", "C#" },
        { "javascript", "JavaScript" },
        { "typescript", "TypeScript" },
        { "html", "HTML" },
        { "css", "CSS" },
        { "json", "JSON" },
        { "xml", "XML" },
        { "razor", "Razor" },
        { "python", "Python" },
        { "java", "Java" },
        { "cpp", "C++" },
        { "sql", "SQL" },
        { "markdown", "Markdown" },
        { "yaml", "YAML" },
        { "dockerfile", "Dockerfile" },
        { "powershell", "PowerShell" },
        { "bash", "Bash" }
    };

    private string GetLanguageDisplayName(string language)
    {
        return availableLanguages.TryGetValue(language, out var displayName) ? displayName : language.ToUpper();
    }

    private void ShowLanguageSelector()
    {
        showLanguageSelector = true;
        StateHasChanged();
    }

    private void HideLanguageSelector()
    {
        showLanguageSelector = false;
        StateHasChanged();
    }

    private async Task SelectLanguage(string language)
    {
        await OnLanguageChanged.InvokeAsync(language);
        HideLanguageSelector();
    }

    private async Task ShowGoToLine()
    {
        var result = await JSRuntime.InvokeAsync<string>("prompt", "Go to line:", LineNumber.ToString());
        if (!string.IsNullOrEmpty(result) && int.TryParse(result, out var lineNumber))
        {
            // Implement go to line functionality
            await JSRuntime.InvokeVoidAsync("showNotification", $"Go to line {lineNumber} functionality would be implemented here");
        }
    }

    public void UpdateErrorCount(int count)
    {
        errorCount = count;
        StateHasChanged();
    }

    public void UpdateWarningCount(int count)
    {
        warningCount = count;
        StateHasChanged();
    }
}
