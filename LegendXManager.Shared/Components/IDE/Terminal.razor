@using Microsoft.JSInterop
@inject IJSRuntime JSRuntime

<div class="terminal" @ref="terminalElement">
    <div class="terminal-output" @ref="outputElement">
        @foreach (var line in outputLines)
        {
            <div class="terminal-line @line.Type">
                <span class="terminal-prompt">@line.Prompt</span>
                <span class="terminal-text">@line.Text</span>
            </div>
        }
    </div>
    
    <div class="terminal-input-container">
        <span class="terminal-prompt">@currentPrompt</span>
        <input @ref="inputElement" 
               @bind="currentInput" 
               @onkeypress="OnKeyPress"
               @onfocus="OnInputFocus"
               class="terminal-input" 
               placeholder="Type a command..." />
    </div>
</div>

@code {
    private ElementReference terminalElement;
    private ElementReference outputElement;
    private ElementReference inputElement;
    
    private List<TerminalLine> outputLines = new();
    private string currentInput = "";
    private string currentPrompt = ">";
    private string currentDirectory = "C:\\";

    public class TerminalLine
    {
        public string Prompt { get; set; } = "";
        public string Text { get; set; } = "";
        public string Type { get; set; } = "normal"; // normal, error, success, info
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await InitializeTerminal();
        }
    }

    private async Task InitializeTerminal()
    {
        await WriteOutput("LegendX IDE Terminal v1.0");
        await WriteOutput("Type 'help' for available commands.");
        await WriteOutput("");
        
        // Focus the input
        await inputElement.FocusAsync();
    }

    private async Task OnKeyPress(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            await ExecuteCommand(currentInput.Trim());
            currentInput = "";
            StateHasChanged();
            
            // Scroll to bottom
            await JSRuntime.InvokeVoidAsync("scrollToBottom", outputElement);
            
            // Refocus input
            await inputElement.FocusAsync();
        }
    }

    private async Task OnInputFocus()
    {
        // Ensure input stays focused
        await Task.Delay(10);
        await inputElement.FocusAsync();
    }

    private async Task ExecuteCommand(string command)
    {
        if (string.IsNullOrWhiteSpace(command))
            return;

        // Add command to output
        outputLines.Add(new TerminalLine 
        { 
            Prompt = currentPrompt, 
            Text = command, 
            Type = "command" 
        });

        // Process command
        await ProcessCommand(command);
    }

    private async Task ProcessCommand(string command)
    {
        var parts = command.Split(' ', StringSplitOptions.RemoveEmptyEntries);
        var cmd = parts[0].ToLower();

        switch (cmd)
        {
            case "help":
                await WriteOutput("Available commands:");
                await WriteOutput("  help          - Show this help message");
                await WriteOutput("  clear         - Clear the terminal");
                await WriteOutput("  echo <text>   - Echo text to output");
                await WriteOutput("  dir           - List directory contents");
                await WriteOutput("  cd <path>     - Change directory");
                await WriteOutput("  dotnet <args> - Run dotnet commands");
                await WriteOutput("  npm <args>    - Run npm commands");
                await WriteOutput("  git <args>    - Run git commands");
                break;

            case "clear":
                outputLines.Clear();
                break;

            case "echo":
                var text = string.Join(" ", parts.Skip(1));
                await WriteOutput(text);
                break;

            case "dir":
            case "ls":
                await WriteOutput("Directory listing for " + currentDirectory);
                await WriteOutput("  bin/");
                await WriteOutput("  obj/");
                await WriteOutput("  src/");
                await WriteOutput("  wwwroot/");
                await WriteOutput("  Program.cs");
                await WriteOutput("  appsettings.json");
                break;

            case "cd":
                if (parts.Length > 1)
                {
                    var newDir = parts[1];
                    currentDirectory = Path.Combine(currentDirectory, newDir);
                    await WriteOutput($"Changed directory to {currentDirectory}");
                }
                else
                {
                    await WriteOutput($"Current directory: {currentDirectory}");
                }
                break;

            case "dotnet":
                await SimulateDotnetCommand(parts.Skip(1).ToArray());
                break;

            case "npm":
                await SimulateNpmCommand(parts.Skip(1).ToArray());
                break;

            case "git":
                await SimulateGitCommand(parts.Skip(1).ToArray());
                break;

            default:
                await WriteError($"'{cmd}' is not recognized as an internal or external command.");
                break;
        }
    }

    private async Task SimulateDotnetCommand(string[] args)
    {
        if (args.Length == 0)
        {
            await WriteOutput("Usage: dotnet [command] [options]");
            return;
        }

        var subCommand = args[0].ToLower();
        switch (subCommand)
        {
            case "build":
                await WriteOutput("Building project...");
                await Task.Delay(1000);
                await WriteSuccess("Build succeeded.");
                break;

            case "run":
                await WriteOutput("Running application...");
                await Task.Delay(500);
                await WriteOutput("Application started. Press Ctrl+C to shut down.");
                break;

            case "test":
                await WriteOutput("Running tests...");
                await Task.Delay(1500);
                await WriteSuccess("Test run completed. 5 passed, 0 failed.");
                break;

            default:
                await WriteOutput($"Executing: dotnet {string.Join(" ", args)}");
                await Task.Delay(500);
                await WriteOutput("Command completed.");
                break;
        }
    }

    private async Task SimulateNpmCommand(string[] args)
    {
        if (args.Length == 0)
        {
            await WriteOutput("Usage: npm <command>");
            return;
        }

        var subCommand = args[0].ToLower();
        switch (subCommand)
        {
            case "install":
                await WriteOutput("Installing packages...");
                await Task.Delay(2000);
                await WriteSuccess("Packages installed successfully.");
                break;

            case "start":
                await WriteOutput("Starting development server...");
                await Task.Delay(1000);
                await WriteOutput("Server running on http://localhost:3000");
                break;

            default:
                await WriteOutput($"Executing: npm {string.Join(" ", args)}");
                await Task.Delay(500);
                await WriteOutput("Command completed.");
                break;
        }
    }

    private async Task SimulateGitCommand(string[] args)
    {
        if (args.Length == 0)
        {
            await WriteOutput("Usage: git <command>");
            return;
        }

        var subCommand = args[0].ToLower();
        switch (subCommand)
        {
            case "status":
                await WriteOutput("On branch main");
                await WriteOutput("Your branch is up to date with 'origin/main'.");
                await WriteOutput("");
                await WriteOutput("Changes not staged for commit:");
                await WriteOutput("  modified:   src/Program.cs");
                break;

            case "add":
                await WriteOutput($"Added {string.Join(" ", args.Skip(1))} to staging area.");
                break;

            case "commit":
                await WriteOutput("Commit created successfully.");
                break;

            default:
                await WriteOutput($"Executing: git {string.Join(" ", args)}");
                await Task.Delay(500);
                await WriteOutput("Command completed.");
                break;
        }
    }

    public async Task WriteOutput(string text, string type = "normal")
    {
        outputLines.Add(new TerminalLine 
        { 
            Prompt = "", 
            Text = text, 
            Type = type 
        });
        StateHasChanged();
        await Task.Delay(10); // Allow UI to update
    }

    public async Task WriteError(string text)
    {
        await WriteOutput(text, "error");
    }

    public async Task WriteSuccess(string text)
    {
        await WriteOutput(text, "success");
    }

    public async Task WriteInfo(string text)
    {
        await WriteOutput(text, "info");
    }

    public async Task Clear()
    {
        outputLines.Clear();
        StateHasChanged();
    }

    public async Task Focus()
    {
        await inputElement.FocusAsync();
    }
}
