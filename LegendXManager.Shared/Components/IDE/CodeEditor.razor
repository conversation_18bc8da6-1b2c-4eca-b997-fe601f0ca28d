@using Microsoft.JSInterop

@inject IJSRuntime JSRuntime


<div class="code-editor" @ref="editorElement">
    <div class="editor-loading" style="display: @(isEditorReady ? "none" : "block")">
        <div class="loading-spinner">
            <i class="bi bi-arrow-repeat spin"></i>
            <span>Loading editor...</span>
        </div>
    </div>
    
    <div class="monaco-editor-container" id="@editorId" style="height: 100%; display: @(isEditorReady ? "block" : "none")"></div>
</div>

@code {
    [Parameter] public string Content { get; set; } = "";
    [Parameter] public string Language { get; set; } = "plaintext";
    [Parameter] public EventCallback<string> OnContentChanged { get; set; }
    [Parameter] public EventCallback<(int line, int column)> OnCursorPositionChanged { get; set; }

    private ElementReference editorElement;
    private string editorId = Guid.NewGuid().ToString();
    private IJSObjectReference? editorModule;
    private IJSObjectReference? editorInstance;
    private bool isEditorReady = false;
    private string lastContent = "";

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await InitializeEditor();
        }
    }

    protected override async Task OnParametersSetAsync()
    {
        if (isEditorReady && Content != lastContent)
        {
            await SetEditorContent(Content);
            lastContent = Content;
        }

        if (isEditorReady && editorInstance != null)
        {
            await SetEditorLanguage(Language);
        }
    }

    private async Task InitializeEditor()
    {
        try
        {
            // Load Monaco Editor
            editorModule = await JSRuntime.InvokeAsync<IJSObjectReference>("import", "./js/monaco-editor.js");
            
            // Initialize the editor
            editorInstance = await editorModule.InvokeAsync<IJSObjectReference>("createEditor", 
                editorId, 
                Content, 
                Language,
                DotNetObjectReference.Create(this));

            isEditorReady = true;
            lastContent = Content;
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error initializing editor: {ex.Message}");
            // Fallback to textarea if Monaco fails to load
            await InitializeFallbackEditor();
        }
    }

    private async Task InitializeFallbackEditor()
    {
        isEditorReady = true;
        StateHasChanged();
    }

    private async Task SetEditorContent(string content)
    {
        if (editorInstance != null)
        {
            await editorInstance.InvokeVoidAsync("setValue", content);
        }
    }

    private async Task SetEditorLanguage(string language)
    {
        if (editorInstance != null)
        {
            await editorInstance.InvokeVoidAsync("setLanguage", language);
        }
    }

    [JSInvokable]
    public async Task OnEditorContentChanged(string content)
    {
        lastContent = content;
        await OnContentChanged.InvokeAsync(content);
    }

    [JSInvokable]
    public async Task OnEditorCursorPositionChanged(int line, int column)
    {
        await OnCursorPositionChanged.InvokeAsync((line, column));
    }

    public async Task Focus()
    {
        if (editorInstance != null)
        {
            await editorInstance.InvokeVoidAsync("focus");
        }
    }

    public async Task InsertText(string text)
    {
        if (editorInstance != null)
        {
            await editorInstance.InvokeVoidAsync("insertText", text);
        }
    }

    public async Task SetTheme(string theme)
    {
        if (editorInstance != null)
        {
            await editorInstance.InvokeVoidAsync("setTheme", theme);
        }
    }

    public async ValueTask DisposeAsync()
    {
        if (editorInstance != null)
        {
            await editorInstance.InvokeVoidAsync("dispose");
            await editorInstance.DisposeAsync();
        }

        if (editorModule != null)
        {
            await editorModule.DisposeAsync();
        }
    }
}
