@using Microsoft.JSInterop

@inject IJSRuntime JsRuntime


<div class="code-editor" @ref="_editorElement">
    <div class="editor-loading" style="display: @(_isEditorReady ? "none" : "block")">
        <div class="loading-spinner">
            <i class="bi bi-arrow-repeat spin"></i>
            <span>Loading editor...</span>
        </div>
    </div>
    
    <div class="monaco-editor-container" id="@_editorId" style="height: 100%; display: @(_isEditorReady ? "block" : "none")"></div>
</div>

@code {
    [Parameter] public string Content { get; set; } = "";
    [Parameter] public string Language { get; set; } = "plaintext";
    [Parameter] public EventCallback<string> OnContentChanged { get; set; }
    [Parameter] public EventCallback<(int line, int column)> OnCursorPositionChanged { get; set; }

    private ElementReference _editorElement;
    private string _editorId = Guid.NewGuid().ToString();
    private IJSObjectReference? _editorModule;
    private IJSObjectReference? _editorInstance;
    private bool _isEditorReady = false;
    private string _lastContent = "";

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await InitializeEditor();
        }
    }

    protected override async Task OnParametersSetAsync()
    {
        if (_isEditorReady && Content != _lastContent)
        {
            await SetEditorContent(Content);
            _lastContent = Content;
        }

        if (_isEditorReady && _editorInstance != null)
        {
            await SetEditorLanguage(Language);
        }
    }

    private async Task InitializeEditor()
    {
        try
        {
            // Load Monaco Editor
            _editorModule = await JsRuntime.InvokeAsync<IJSObjectReference>("import", "./js/monaco-editor.js");
            
            // Initialize the editor
            _editorInstance = await _editorModule.InvokeAsync<IJSObjectReference>("createEditor", 
                _editorId, 
                Content, 
                Language,
                DotNetObjectReference.Create(this));

            _isEditorReady = true;
            _lastContent = Content;
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error initializing editor: {ex.Message}");
            // Fallback to textarea if Monaco fails to load
            await InitializeFallbackEditor();
        }
    }

    private async Task InitializeFallbackEditor()
    {
        _isEditorReady = true;
        StateHasChanged();
    }

    private async Task SetEditorContent(string content)
    {
        if (_editorInstance != null)
        {
            await _editorInstance.InvokeVoidAsync("setValue", content);
        }
    }

    private async Task SetEditorLanguage(string language)
    {
        if (_editorInstance != null)
        {
            await _editorInstance.InvokeVoidAsync("setLanguage", language);
        }
    }

    [JSInvokable]
    public async Task OnEditorContentChanged(string content)
    {
        _lastContent = content;
        await OnContentChanged.InvokeAsync(content);
    }

    [JSInvokable]
    public async Task OnEditorCursorPositionChanged(int line, int column)
    {
        await OnCursorPositionChanged.InvokeAsync((line, column));
    }

    public async Task Focus()
    {
        if (_editorInstance != null)
        {
            await _editorInstance.InvokeVoidAsync("focus");
        }
    }

    public async Task InsertText(string text)
    {
        if (_editorInstance != null)
        {
            await _editorInstance.InvokeVoidAsync("insertText", text);
        }
    }

    public async Task SetTheme(string theme)
    {
        if (_editorInstance != null)
        {
            await _editorInstance.InvokeVoidAsync("setTheme", theme);
        }
    }

    public async ValueTask DisposeAsync()
    {
        if (_editorInstance != null)
        {
            await _editorInstance.InvokeVoidAsync("dispose");
            await _editorInstance.DisposeAsync();
        }

        if (_editorModule != null)
        {
            await _editorModule.DisposeAsync();
        }
    }
}
